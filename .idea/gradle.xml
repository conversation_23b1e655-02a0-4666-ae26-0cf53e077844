<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="GradleMigrationSettings" migrationVersion="1" />
  <component name="GradleSettings">
    <option name="linkedExternalProjectsSettings">
      <GradleProjectSettings>
        <option name="externalProjectPath" value="$PROJECT_DIR$" />
        <option name="gradleJvm" value="17" />
        <option name="modules">
          <set>
            <option value="$PROJECT_DIR$" />
            <option value="$PROJECT_DIR$/common-stop-list" />
            <option value="$PROJECT_DIR$/lib-stop-list" />
            <option value="$PROJECT_DIR$/stop-list-api" />
            <option value="$PROJECT_DIR$/stop-list-gate" />
            <option value="$PROJECT_DIR$/stop-list-model" />
            <option value="$PROJECT_DIR$/stop-list-private-api" />
            <option value="$PROJECT_DIR$/stop-list-processing" />
          </set>
        </option>
      </GradleProjectSettings>
    </option>
  </component>
</project>