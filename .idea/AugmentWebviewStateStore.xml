<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;b335b2ac-0400-489d-b35b-2d8871b12353&quot;,&quot;conversations&quot;:{&quot;f03b3a29-3639-4c9c-a4ca-eae2b90b44f6&quot;:{&quot;id&quot;:&quot;f03b3a29-3639-4c9c-a4ca-eae2b90b44f6&quot;,&quot;createdAtIso&quot;:&quot;2025-07-14T07:42:06.897Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-14T07:42:06.897Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;46a1b244-18e3-4afd-9651-95a788cdde43&quot;},&quot;b335b2ac-0400-489d-b35b-2d8871b12353&quot;:{&quot;id&quot;:&quot;b335b2ac-0400-489d-b35b-2d8871b12353&quot;,&quot;createdAtIso&quot;:&quot;2025-07-14T07:42:07.053Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-14T07:59:56.557Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;24a70b2a-921b-4ea6-a3a3-1d5b53221712&quot;,&quot;request_message&quot;:&quot;Есть проект на kotlin c несколькими gradle модулями.\nТак же подключен плагин для выпуска релизов.\nНужно настроить таким образом чтобы при выпуске релиза\n1)  релиз выпускался одним коммитом\n2) После релиза происходила сборка и публикация в nexus&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;Есть проект на kotlin c несколькими gradle модулями.&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;Так же подключен плагин для выпуска релизов.&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;Нужно настроить таким образом чтобы при выпуске релиза&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;1)  релиз выпускался одним коммитом&quot;},{&quot;type&quot;:&quot;hardBreak&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;2) После релиза происходила сборка и публикация в nexus&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:1,&quot;type&quot;:2,&quot;content&quot;:&quot;*Your subscription <NAME_EMAIL> is inactive. If you have another account with an active subscription, please sign out and sign in with that one. Otherwise, update your plan [here](https://app.augmentcode.com/account) to continue using Augment.*&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;Есть проект на kotlin c несколькими gradle модулями.\nТак же подключен плагин для выпуска релизов.\nНужно настроить таким образом чтобы при выпуске релиза\n1)  релиз выпускался одним коммитом\n2) После релиза происходила сборка и публикация в nexus&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;/home/<USER>/projects/st2/stop-list-domain&quot;,&quot;folderRoot&quot;:&quot;/home/<USER>/projects/st2/stop-list-domain&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-14T07:59:56.557Z&quot;,&quot;response_text&quot;:&quot;*Your subscription <NAME_EMAIL> is inactive. If you have another account with an active subscription, please sign out and sign in with that one. Otherwise, update your plan [here](https://app.augmentcode.com/account) to continue using Augment.*&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-2e166cbd-087d-4d47-b1f9-7586b4a3e8f5&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;9dde5987-8e26-4a6d-8b1a-86915d260bc2&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>