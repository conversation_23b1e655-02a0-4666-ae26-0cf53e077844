syntax = "proto3";

package ru.sbertroika.stop.list.gate.v1;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "common.proto";
import "common-stop-list.proto";

option java_multiple_files = true;
option java_package = "ru.sbertroika.stop.list.gate.v1";

service StopListGateService {
  // Запрос обновления стоп-листа
  rpc getStopListUpdate(StopListUpdateRequest) returns (StopListUpdateResponse);
}

message StopListUpdateRequest {
  string projectId = 1;                       // Идентификатор проекта
  uint64 version = 2;                         // Текущая версия стоп-листа на терминале
  common.stop.list.StopListType type = 3;     // Тип стоп-листа
}

message StopListUpdateResponse {
  oneof response {
    common.v1.OperationError error = 1;
    common.stop.list.StopListUpdate result = 2;
  }
}