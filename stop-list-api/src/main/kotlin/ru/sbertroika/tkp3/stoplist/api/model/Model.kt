package ru.sbertroika.tkp3.stoplist.api.model

import java.util.*

/**
 * Стоп-лист
 */
data class EmvStopList(

    /**
     * Идентификатор проекта
     */
    val project: UUID,

    /**
     * Версия стоп-листа
     */
    val version: Long,

    /**
     * Записи стоп-листа
     */
    val records: List<EmvStopListOperationRecord>,

    /**
     * Требуется ли полный сброс перед применением операций
     */
    val isReset: Boolean = false
)

/**
 * Запись стоп-листа
 */
data class EmvStopListOperationRecord(

    /**
     * Идентификатор записи стоп-листа
     */
    val hash: String,

    /**
     * Признак Payment Account Reference
     */
    val hashType: HashType,

    /**
     * Тип операции над записью стоп-листа
     */
    val type: StopListOperationType
)

enum class HashType {
    SHA256, PAR, HMAC_SHA1, BIN, HMAC_SHA256, HMAC_SHA256_SHA256, STRIBOK512
}

enum class StopListOperationType {

    /**
     * Добавление записи
     */
    APPEND,

    /**
     * Изменение существующей записи
     */
    CHANGE,

    /**
     * Удаление существующей записи
     */
    DELETE
}

/**
 * Виртуальный стоп-лист
 */
data class VirtualStopList(

    /**
     * Идентификатор проекта
     */
    val project: UUID,

    /**
     * Записи виртуального стоп-листа
     */
    val records: List<VirtualStopListOperationRecord>,

    /**
     * Требуется ли полный сброс перед применением операций
     */
    val isReset: Boolean = false
)

/**
 * Запись виртуального стоп-листа
 */
data class VirtualStopListOperationRecord(

    /**
     * Печатный номер карты
     */
    val num: String,

    /**
     * Тип операции над записью стоп-листа
     */
    val type: StopListOperationType
)
