# Stop List Domain - Система управления стоп-листами

## Обзор

Проект представляет собой комплексную систему для работы со стоп-листами, состоящую из:
- **Библиотечных модулей** для переиспользования логики
- **Микросервисов** для обработки и управления стоп-листами
- **API сервисов** для внешнего взаимодействия

Система построена на Kotlin/Spring Boot с автоматическим CI/CD pipeline для разработки, тестирования и развертывания.

## Архитектура

### Библиотечные модули
- `common-stop-list` - Общая логика и утилиты для работы со стоп-листами
- `stop-list-api` - Публичное API для работы со стоп-листами
- `stop-list-private-api` - Приватное API для внутренних сервисов
- `stop-list-model` - Модели данных и DTO
- `lib-stop-list` - Дополнительные библиотечные компоненты

### Микросервисы
- `stop-list-gate` - API Gateway для внешних запросов
- `stop-list-processing` - Сервис обработки стоп-листов

## Структура проекта

```
stop-list-domain/
├── common-stop-list/           # Общий модуль
├── stop-list-api/              # Публичное API
├── stop-list-private-api/      # Приватное API
├── stop-list-model/            # Модели данных
├── lib-stop-list/              # Дополнительные библиотеки
├── stop-list-gate/             # API Gateway микросервис
├── stop-list-processing/       # Сервис обработки
├── charts/                     # Helm чарты для Kubernetes
│   ├── stop-list-gate/
│   └── stop-list-processing/
├── scripts/                    # Скрипты для CI/CD
├── docs/                       # Документация
├── data/                       # Данные для локальной разработки
├── docker-compose.yml          # Локальное окружение
├── build.gradle.kts            # Корневая конфигурация Gradle
├── .gitlab-ci.yml              # GitLab CI/CD pipeline
└── gradle.properties           # Версия проекта
```

## Технологический стек

- **Язык**: Kotlin
- **Фреймворк**: Spring Boot
- **База данных**: PostgreSQL
- **Очереди**: Apache Kafka
- **Хранилище**: S3/MinIO
- **Контейнеризация**: Docker
- **Оркестрация**: Kubernetes + Helm
- **CI/CD**: GitLab CI/CD
- **Артефакты**: Nexus Repository

## Быстрый старт

### Предварительные требования

- Java 17
- Docker & Docker Compose
- Gradle 8.x (или используйте gradlew)
- Git

### Локальная разработка

1. **Клонирование репозитория**
```bash
git clone <repository-url>
cd stop-list-domain
```

2. **Сборка библиотечных модулей**
```bash
./gradlew build
```

3. **Запуск локального окружения**
```bash
# Запуск инфраструктуры (PostgreSQL, Kafka, S3, ClickHouse)
docker-compose up -d stop-list-db kafka s3 clickhouse-server kafka-ui s3-ui

# Создание S3 bucket через UI (http://localhost:9001)
# Логин: s3__user, Пароль: s3__pass
```

4. **Сборка и запуск микросервисов**
```bash
# Сборка Docker образов
docker build -f stop-list-gate/Dockerfile -t stop-list-gate:local .
docker build -f stop-list-processing/Dockerfile -t stop-list-processing:local .

# Запуск микросервисов
docker-compose up stop-list-gate stop-list-processing
```

### Доступные сервисы

- **API Gateway**: http://localhost:8010
- **Kafka UI**: http://localhost:8080
- **S3 UI**: http://localhost:9001
- **PostgreSQL**: localhost:5432

## CI/CD Pipeline

Проект использует GitLab CI/CD с Docker executor для автоматизации:

### Сценарии pipeline:

1. **Любая ветка (кроме develop/master)**
   - Сборка и тестирование библиотечных модулей

2. **Ветка develop**
   - Сборка и тестирование
   - Публикация SNAPSHOT версий в Nexus
   - Сборка и деплой микросервисов в тестовое окружение

3. **Ветка master / Теги**
   - Выпуск релизных версий
   - Публикация в Nexus
   - Сборка и деплой микросервисов в продакшн

### Особенности:
- Использует Docker executor с тегом `docker`
- Автоматическое создание `gradle.properties` из переменных окружения
- Кэширование Gradle для ускорения сборок
- Helm чарты для Kubernetes деплоя
- Валидация Kubernetes манифестов с помощью kubeval

## Разработка

### Структура веток
- `master` - основная ветка для релизов
- `develop` - ветка для разработки и тестирования
- `feature/*` - ветки для новых функций
- `release/*` - ветки для подготовки релизов
- `hotfix/*` - ветки для критических исправлений

### Процесс разработки
1. Создать feature ветку от `develop`
2. Разработать функциональность
3. Создать MR в `develop`
4. После тестирования создать MR из `develop` в `master`

### Полезные команды

```bash
# Сборка проекта
./gradlew build

# Запуск тестов
./gradlew test

# Публикация SNAPSHOT (локально)
./gradlew publishToNexusSnapshot

# Публикация релиза (локально)
./gradlew publishToNexus

# Выпуск релиза
./gradlew release

# Тестирование CI/CD локально
./test-docker-ci.sh all
```

## Конфигурация

### Переменные окружения для микросервисов

**stop-list-gate:**
- `DB_URL` - URL PostgreSQL базы данных
- `R2DB_URL` - URL для R2DBC подключения
- `KAFKA_SERVERS` - Адреса Kafka брокеров
- `S3_URL` - URL S3 хранилища
- `CLICKHOUSE_URL` - URL ClickHouse

**stop-list-processing:**
- `DEVELOP_EMISSION_STUB` - Использование stub emission сервиса
- `DEVELOP_S3_DISABLE_CHECKSUM` - Отключение checksum для совместимости с MinIO

### Настройка CI/CD

Для работы pipeline необходимо настроить переменные в GitLab:

1. Перейти в **Settings** → **CI/CD** → **Variables**
2. Добавить переменные:
   - `MAVEN_USER` (Protected)
   - `MAVEN_PASSWORD` (Protected + Masked)
   - `DOCKER_REPOSITORY_ADDR` - Адрес Docker registry
   - `KUBECONFIG_DEVELOP` - Kubeconfig для тестового кластера

## Публикация в Nexus

Проект настроен для публикации в Nexus репозиторий:

- **SNAPSHOT**: `https://nexus.sbertroika.tech/repository/maven-snapshots/`
- **Releases**: `https://nexus.sbertroika.tech/repository/maven-releases/`

### Артефакты:
- `ru.sbertroika.stop-list:common-stop-list:${version}`
- `ru.sbertroika.stop-list:stop-list-api:${version}`
- `ru.sbertroika.stop-list:stop-list-private-api:${version}`
- `ru.sbertroika.stop-list:stop-list-model:${version}`
- `ru.sbertroika.stop-list:lib-stop-list:${version}`

## Развертывание

### Kubernetes

Микросервисы развертываются в Kubernetes с помощью Helm чартов:

```bash
# Деплой в тестовое окружение
helm upgrade stop-list-gate charts/stop-list-gate \
  --install \
  --set namespace=sl \
  --set image.tag=develop

# Деплой в продакшн
helm upgrade stop-list-gate charts/stop-list-gate \
  --install \
  --set namespace=sl \
  --set image.tag=v1.0.0
```

### Docker

Для локального развертывания используйте docker-compose:

```bash
# Полное окружение
docker-compose up -d

# Только инфраструктура
docker-compose up -d stop-list-db kafka s3 clickhouse-server
```

## Troubleshooting

### Ошибки сборки
- Убедиться, что используется Java 17
- Проверить, что все зависимости доступны в Nexus

### Ошибки CI/CD
- Проверить настройки переменных окружения в GitLab
- Убедиться, что GitLab runner с тегом `docker` доступен

### Ошибки локального запуска
- Проверить, что Docker и Docker Compose установлены
- Убедиться, что порты не заняты другими сервисами
- Проверить логи контейнеров: `docker-compose logs <service-name>`

### Ошибки публикации
- Проверить учетные данные Nexus
- Убедиться, что версия корректна и не конфликтует

## Документация

Дополнительная документация находится в папке `docs/`:
- `DOCKER_CI_SETUP.md` - Настройка CI/CD
- `CI_CD_SUMMARY.md` - Сводка по CI/CD
- `GITLAB_CI_SETUP.md` - Переменные GitLab
- `RELEASE_INSTRUCTIONS.md` - Инструкции по релизу

## Контакты

Для вопросов по проекту обращайтесь к команде разработки. 