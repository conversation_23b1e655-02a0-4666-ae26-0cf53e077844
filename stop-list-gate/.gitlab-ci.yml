include:
  - project: 'tkp3/infra'
    file: '/gitlab_templates/java-autoload.templates.yaml'
    ref: OPDVST-2352-security-updates

# ========================
# STOP-LIST-GATE: build + deploy (docker + helm)
# ========================

# --- DEVELOP ветка ---
stop_list_gate_build_develop:
  stage: build
  needs: [develop_build_test_publish]
  variables:
    SERVICE_NAME: "stop-list-gate"
    TAG: "$CI_COMMIT_SHORT_SHA"
  extends:
    - .docker_gradle_build_and_push
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - stop-list-gate/**
        - ./*

stop_list_gate_helm_kubeval_testing_develop:
  stage: test
  needs: [stop_list_gate_build_develop]
  variables:
    SERVICE_NAME: "stop-list-gate"
  extends:
    - .validate_helm_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - stop-list-gate/**
        - charts/stop-list-gate/**

stop_list_gate_deploy_chart_develop:
  stage: deploy
  needs:
    - stop_list_gate_helm_kubeval_testing_develop
    - job: stop_list_gate_build_develop
      optional: true # need to run re-deploy on chart change without rebuilding
  variables:
    STAGE: "dev"
    SERVICE_NAME: "stop-list-gate"
  extends:
    - .deploy_helm_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - stop-list-gate/**
        - charts/stop-list-gate/**

# --- TAG ---
.tag_rules: &tag_rules
  rules:
    - if: $CI_COMMIT_TAG
      changes:
        - stop-list-gate/**

stop_list_gate_build_tag:
  stage: build
  variables:
    TAG: "$CI_COMMIT_TAG"
    SERVICE_NAME: "stop-list-gate"
  extends:
    - .docker_gradle_build_and_push
  <<: *tag_rules

stop_list_gate_helm_kubeval_testing_tag:
  stage: test
  needs:
    - stop_list_gate_build_tag
  variables:
    SERVICE_NAME: "stop-list-gate"
  extends:
    - .validate_helm_template
  <<: *tag_rules

stop_list_gate_deploy_chart_tag:
  stage: deploy
  needs:
    - stop_list_gate_helm_kubeval_testing_tag
  variables:
    STAGE: "dev"
    TAG: "$CI_COMMIT_TAG"
    SERVICE_NAME: "stop-list-gate"
  extends:
    - .deploy_helm_template
  <<: *tag_rules
