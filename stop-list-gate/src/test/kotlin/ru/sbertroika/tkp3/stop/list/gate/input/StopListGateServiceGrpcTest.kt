package ru.sbertroika.tkp3.stop.list.gate.input

import io.grpc.*
import io.netty.handler.ssl.util.InsecureTrustManagerFactory
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Test
import ru.sbertroika.common.stop.list.StopListType
import ru.sbertroika.stop.list.gate.v1.StopListGateServiceGrpcKt
import ru.sbertroika.stop.list.gate.v1.stopListUpdateRequest

class StopListGateServiceGrpcTest {

    companion object {
        private const val server = "localhost"
        private const val port = 5005
        private const val isTls = false
    }

    @Test
    fun getStopListUpdateTest(): Unit = runBlocking {
        val response = client().getStopListUpdate(
            stopListUpdateRequest {
                projectId = "43db9de7-72ec-4eae-8978-8aef1c46873a"
                version = 0
                type = StopListType.SL_EMV
            }
        )
        println("response: $response")
        assertFalse(response.hasError())
    }

    private fun client(): StopListGateServiceGrpcKt.StopListGateServiceCoroutineStub {
        return if (isTls) {
            val credentials: ChannelCredentials = TlsChannelCredentials.newBuilder() //You can use your own certificate here .trustManager(new File("cert.pem"))
                .trustManager(InsecureTrustManagerFactory.INSTANCE.trustManagers[0])
                .build()
            val channel: ManagedChannel = Grpc.newChannelBuilderForAddress(server, port, credentials)
                .build()
            return StopListGateServiceGrpcKt.StopListGateServiceCoroutineStub(channel)
        } else {
            val channel = ManagedChannelBuilder.forTarget("$server:$port")
                .usePlaintext()
                .build()
            StopListGateServiceGrpcKt.StopListGateServiceCoroutineStub(channel)
        }
    }
}