package ru.sbertroika.tkp3.stop.list.gate.input

import io.grpc.*
import io.netty.handler.ssl.util.InsecureTrustManagerFactory
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Test
import ru.sbertroika.common.Pagination
import ru.sbertroika.common.stop.list.StopListType
import ru.sbertroika.common.v1.PaginationRequest
import ru.sbertroika.stop.list.gate.v1.*

class StopListGatePrivateServiceGrpcTest {

    companion object {
        private const val server = "localhost"
        private const val port = 5005
        private const val isTls = false
    }

    @Test
    fun journalList(): Unit = runBlocking {
        val response = client().journalList(
            stopListJournalRequest {
                pagination = PaginationRequest.newBuilder().setPage(0).setLimit(25).build()
                filter = stopListJournalFilter {
                    uid = "11683807151921809998"
                }
            }
        )
        println("response: $response")
        assertFalse(response.hasError())
    }

    private fun client(): StopListGatePrivateServiceGrpcKt.StopListGatePrivateServiceCoroutineStub {
        return if (isTls) {
            val credentials: ChannelCredentials = TlsChannelCredentials.newBuilder() //You can use your own certificate here .trustManager(new File("cert.pem"))
                .trustManager(InsecureTrustManagerFactory.INSTANCE.trustManagers[0])
                .build()
            val channel: ManagedChannel = Grpc.newChannelBuilderForAddress(server, port, credentials)
                .build()
            return StopListGatePrivateServiceGrpcKt.StopListGatePrivateServiceCoroutineStub(channel)
        } else {
            val channel = ManagedChannelBuilder.forTarget("$server:$port")
                .usePlaintext()
                .build()
            StopListGatePrivateServiceGrpcKt.StopListGatePrivateServiceCoroutineStub(channel)
        }
    }
}