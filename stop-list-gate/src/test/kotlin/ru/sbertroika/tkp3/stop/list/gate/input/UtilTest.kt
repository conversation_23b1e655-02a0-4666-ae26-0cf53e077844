package ru.sbertroika.tkp3.stop.list.gate.input

import org.junit.jupiter.api.Test
import ru.sbertroika.tkp3.stop.list.gate.util.toLong
import java.util.UUID

class UtilTest {

    @Test
    fun testUUIDToLong() {
        println("----------")
        println(UUID.fromString("b234f7d2-1ae1-49da-8338-98588280b4ad").toLong())
        println(UUID.fromString("00000000-0000-0000-0000-000000000000").toLong())
        println(UUID.fromString("00000000-0000-0000-0000-000000000001").toLong())
        println(UUID.fromString("11111111-1111-1111-1111-111111111111").toLong())
        println(UUID.fromString("00000000-1111-2222-3333-************").toLong())
        println(UUID.fromString("*************-7654-3210-************").toLong())
        println(UUID.fromString("ffffffff-ffff-ffff-ffff-fffffffffffe").toLong())
        println(UUID.fromString("ffffffff-ffff-ffff-ffff-ffffffffffff").toLong())
    }
}