package ru.sbertroika.tkp3.stop.list.gate.input

import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.doReturn
import org.mockito.Mockito.mock
import org.springframework.util.Assert
import ru.sbertroika.stop.list.model.db.VirtStopList
import ru.sbertroika.stop.list.model.db.VirtStopListRecord
import ru.sbertroika.tkp3.stop.list.gate.output.repository.EmvStopListRepository
import ru.sbertroika.tkp3.stop.list.gate.output.repository.VirtStopListRecordRepository
import ru.sbertroika.tkp3.stop.list.gate.output.repository.VirtStopListRepository
import ru.sbertroika.tkp3.stop.list.gate.service.StopListService
import ru.sbertroika.tkp3.stop.list.gate.service.impl.StopListServiceImpl
import ru.sbertroika.tkp3.stop.list.gate.util.toLong
import ru.sbertroika.tkp3.stoplist.api.model.VirtualStopList
import java.sql.Timestamp
import java.util.UUID

class StopListServiceImplTest {


    private val emvStopListRepository: EmvStopListRepository = mock(EmvStopListRepository::class.java)
    private val virtStopListRecordRepository: VirtStopListRecordRepository = mock(VirtStopListRecordRepository::class.java)
    private val virtStopListRepository: VirtStopListRepository = mock(VirtStopListRepository::class.java)
    private val baseUrl = "http://localhost:8080"

    private lateinit var service: StopListService

    @BeforeEach
    fun setup() {
        service = StopListServiceImpl(
            emvStopListRepository,
            virtStopListRecordRepository,
            virtStopListRepository,
            baseUrl)
    }

    @Test
    fun getVirtStopList(): Unit = runBlocking {
        val prevVersion = 20240208154100
        val version = 20240208154200
        val nextVersion = 20240208154300
        val uuid = UUID.fromString("fa3fa07c-805b-4d2f-ab3e-828fe03d9289")
        val timestamp = Timestamp(1707396508)

        val vsl = VirtStopList(
            uuid,
            uuid,
            timestamp,
            version
        )

        val uid = "7000000000000001"

        val vslr = VirtStopListRecord(uuid, UUID.randomUUID(), timestamp, version, UUID.randomUUID(), uid)

        doReturn(vsl).`when`(virtStopListRepository).findTopByOrderByVersionDesc()
        doReturn(flowOf(vslr)).`when`(virtStopListRecordRepository).findAll()

        service.getVirtStopList(prevVersion).fold({
            assertFalse(true)
        }, {
            println("it: $it")
            assertTrue(it.data.stopList.isNotEmpty())
            assertTrue(it.data.stopList.size == 1)
            assertTrue(it.data.stopList[0].size == 4)
            assertTrue(it.data.stopList[0][0] == uuid.toLong())
            assertTrue(it.data.stopList[0][1] == 16)
            assertTrue(it.data.stopList[0][2] == uid)
            assertTrue(it.data.stopList[0][3] == 1)
        })

        service.getVirtStopList(version).fold({
            assertFalse(true)
        }, {
            println("it: $it")
            assertTrue(it.data.stopList.isEmpty())
        })


        service.getVirtStopList(nextVersion).fold({
            assertFalse(true)
        }, {
            println("it: $it")
            assertTrue(it.data.stopList.isEmpty())
        })

    }
}