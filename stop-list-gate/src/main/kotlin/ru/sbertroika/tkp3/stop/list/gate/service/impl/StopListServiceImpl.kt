package ru.sbertroika.tkp3.stop.list.gate.service.impl

import arrow.core.Either
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toList
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import ru.sbertroika.common.stop.list.*
import ru.sbertroika.tkp3.stop.list.gate.model.EmvStopListUpdate
import ru.sbertroika.tkp3.stop.list.gate.model.StopListJson
import ru.sbertroika.tkp3.stop.list.gate.model.StopListJsonModel
import ru.sbertroika.tkp3.stop.list.gate.output.repository.EmvStopListRepository
import ru.sbertroika.tkp3.stop.list.gate.output.repository.VirtStopListRecordRepository
import ru.sbertroika.tkp3.stop.list.gate.output.repository.VirtStopListRepository
import ru.sbertroika.tkp3.stop.list.gate.service.StopListService
import ru.sbertroika.tkp3.stop.list.gate.util.toLong
import ru.sbertroika.tkp3.stoplist.api.model.HashType
import java.math.BigInteger
import java.nio.ByteBuffer
import java.util.*


@Service
class StopListServiceImpl(
    private val emvStopListRepository: EmvStopListRepository,
    private val virtStopListRecordRepository: VirtStopListRecordRepository,
    private val virtStopListRepository: VirtStopListRepository,
    @Value("\${base_url}")
    private val baseUrl: String
) : StopListService {

    override suspend fun findStopListUpdate(projectId: UUID, sType: StopListType, version: Long): Either<Throwable, StopListUpdate> = Either.catch {
        when (sType) {
            StopListType.SL_EMV -> {
                findEmvStopListUpdate(projectId, version).fold(
                    {
                        throw it
                    },
                    { res ->
                        val stopLists = res.map { item ->
                            stopList {
                                this.updateType = if (item.isFull) StopListUpdateType.SLU_FULL else StopListUpdateType.SLU_DIFF
                                this.subType = item.type
                                this.url = item.url
                                this.crc = item.crc
                            }
                        }.toList()
                        stopListUpdate {
                            this.version = res.maxOf { it.version }
                            this.type = sType
                            this.list += stopLists
                        }
                    }
                )
            }

            StopListType.SL_TROIKA -> stopListUpdate {
                this.version = version
                this.type = sType
            }

            StopListType.SL_PROSTOR -> stopListUpdate {
                this.version = version
                this.type = sType
            }

            StopListType.UNRECOGNIZED -> stopListUpdate {
                this.version = version
                this.type = sType
            }
        }
    }

    override suspend fun getVirtStopList(version: Long): Either<Throwable, StopListJsonModel> = try {
        val maxResult = virtStopListRepository.findTopByOrderByVersionDesc()
        val result = virtStopListRecordRepository.findAll().map { listOf(it.id?.toLong(), 16, it.uid, 1) }.toList()
        if(maxResult == null)
            Either.Right(StopListJsonModel(
                StopListJson(
                    0,0, arrayListOf("Id", "Type", "PANHash", "IsBlocked"), arrayListOf()
                )
            ))
        else
            Either.Right(StopListJsonModel(
                StopListJson(
                    maxResult.version!!,
                    maxResult.version!!,
                    arrayListOf("Id", "Type", "PANHash", "IsBlocked"),
                    if(maxResult.version!! <= version) arrayListOf() else result
                )
            ))
    } catch (e: Exception) {
        Either.Left(Error(e))
    }

    private suspend fun findEmvStopListUpdate(projectId: UUID, version: Long): Either<Throwable, List<EmvStopListUpdate>> = Either.catch {
        val last = if (version > 0) {
            emvStopListRepository.findLast(
                projectId = projectId,
                version = version
            )
        } else {
            emvStopListRepository.findMax(
                projectId = projectId
            )
        }

        if (last.isEmpty()) {
            emptyList()
        } else {
            val groups = last.groupByTo(mutableMapOf()) { it.hashType }
            groups.map {
                val update = if (it.value.size > 1) it.value.last() else it.value.first()
                EmvStopListUpdate(
                    version = update.version!!,
                    isFull = true,
                    url = "$baseUrl/sl/file/${update.fullFileName}",
                    crc = update.fullCrc32!!,
                    type = StopListSubType.valueOf("SLH_${HashType.values()[update.hashType!!]}")
                )
            }.toList()
        }
    }
}