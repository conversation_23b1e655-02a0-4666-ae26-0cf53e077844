package ru.sbertroika.tkp3.stop.list.gate.input

import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.io.InputStreamResource
import org.springframework.core.io.Resource
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toMono
import ru.sbertroika.tkp3.stop.list.gate.model.StopListJsonModel
import ru.sbertroika.tkp3.stop.list.gate.service.StopListFileService
import ru.sbertroika.tkp3.stop.list.gate.service.StopListService


@RestController
class StopListGateController(
    @Value("\${input_log.enabled}")
    private var inputLog: <PERSON><PERSON><PERSON>,

    private val stopListFileService: StopListFileService,
    private val stopListService: StopListService
) {
    private val log = LoggerFactory.getLogger(this::class.java)

    @GetMapping("/sl/file/{file}")
    suspend fun getSlFile(@PathVariable file: String): Mono<ResponseEntity<Resource>> {
        if (inputLog) {
            log.info("REQUEST /getSlFile file=$file")
        }

        return stopListFileService.getFile(file).fold(
            {
                log.error("Error get file", it)
                ResponseEntity(HttpStatus.NOT_FOUND)
            },
            { responseStream ->
                val resource = InputStreamResource(responseStream)
                val headers = HttpHeaders()

                headers.add(HttpHeaders.CONTENT_DISPOSITION, ("attachment; filename=\"" + file.substring(file.lastIndexOf('/') + 1)).toString() + "\"")

                ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(responseStream.response().contentLength())
                    .contentType(MediaType.parseMediaType(responseStream.response().contentType()))
                    .body<Resource>(resource)
            }
        ).toMono()
    }

    @GetMapping("/v1/tkp2/sl/virt")
    suspend fun getSlVirt(@RequestParam("version") version: Long): Mono<ResponseEntity<StopListJsonModel>> {
        if (inputLog) {
            log.info("REQUEST /v1/tkp2/sl/virt version=$version")
        }

        return stopListService.getVirtStopList(version).fold(
            {
                log.error("Error virt stop list not found", it)
                ResponseEntity(HttpStatus.NOT_FOUND)
            },
            { model ->
                ResponseEntity.ok()
                    .body(model)
            }
        ).toMono()
    }
}