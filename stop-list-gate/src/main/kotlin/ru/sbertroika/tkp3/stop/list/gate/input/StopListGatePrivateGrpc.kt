package ru.sbertroika.tkp3.stop.list.gate.input

import org.lognet.springboot.grpc.GRpcService
import ru.sbertroika.common.Pagination
import ru.sbertroika.common.stop.list.StopListSubType
import ru.sbertroika.common.stop.list.StopListType
import ru.sbertroika.common.toOperationError
import ru.sbertroika.common.toTimestamp
import ru.sbertroika.common.toZonedDateTime
import ru.sbertroika.common.v1.ErrorType
import ru.sbertroika.common.v1.OperationError
import ru.sbertroika.common.v1.PaginationRequest
import ru.sbertroika.common.v1.PaginationResponse
import ru.sbertroika.stop.list.gate.v1.*
import ru.sbertroika.tkp3.stop.list.gate.model.JournalFilter
import ru.sbertroika.tkp3.stop.list.gate.output.model.StopListJournal
import ru.sbertroika.tkp3.stop.list.gate.service.JournalService
import java.math.BigInteger

@GRpcService(port = 5005)
class StopListGatePrivateGrpc(
    val journalService: JournalService
): StopListGatePrivateServiceGrpcKt.StopListGatePrivateServiceCoroutineImplBase() {

    override suspend fun journalList(request: StopListJournalRequest): StopListJournalResponse {
        return journalService.journalList(
            filter = toJournalFilter(request.filter),
            pagination = toPagination(request.pagination)
        ).fold({
            stopListJournalResponse {
                error = toOperationError(Error(it))
            }
        },
            { res ->
                stopListJournalResponse {
                    result = stopListJournalResult {
                        pagination = toPaginationResponse(res.pagination)
                        record += res.result.map(::toStopListRecord).toList()
                    }
                }
            }
        )
    }

    private fun toJournalFilter(filter: StopListJournalFilter?): JournalFilter {
        if (filter == null) return JournalFilter()
        return JournalFilter(
            cardNum = if (filter.hasCardNum()) filter.cardNum else null,
            type = filter.typeList.ifEmpty { emptyList() },
            createdAtFrom = if (filter.hasCreatedAtFrom()) filter.createdAtFrom?.toZonedDateTime() else null,
            createdAtTo = if (filter.hasCreatedAtTo()) filter.createdAtTo?.toZonedDateTime() else null,
            hash = if (filter.hasHash()) filter.hash else null,
            hashType = if (filter.hasHashType()) filter.hashType else null,
            uid = if (filter.hasUid()) BigInteger(filter.uid) else null,
            createVer = if (filter.hasCreateVer()) filter.createVer else null
        )
    }

    private fun toStopListRecord(event: StopListJournal): StopListRecord {
        val r = StopListRecord.newBuilder()
            .setCreatedAt(event.createdAt?.toTimestamp())
            .setRecordAt(event.recordAt?.toTimestamp())
            .setProjectId(event.projectId)
            .setOperation(event.operation)
            .setType(StopListType.valueOf("SL_" + event.type))
            .setCreateVer(event.createVer ?: -1)
        event.uid?.let { r.setUid(it.toString()) }
        event.deleteVer?.let { r.setDeleteVer(it) }
        event.deletedAt?.let { r.setDeletedAt(it.toTimestamp()) }
        event.hash?.let { r.setHash(it) }
        event.hashType?.let { r.setHashType(StopListSubType.valueOf("SLH_" + it.name)) }
        return r.build()
    }

    private fun toPagination(pagination: PaginationRequest?): Pagination {
        if (pagination == null) return Pagination(page = 1, limit = 25)
        return Pagination(
            page = pagination.page,
            limit = if (pagination.limit <= 0) 25 else pagination.limit
        )
    }

    private fun toPaginationResponse(pagination: Pagination): PaginationResponse = PaginationResponse.newBuilder()
        .setPage(pagination.page)
        .setLimit(pagination.limit)
        .setTotalCount(pagination.totalCount!!)
        .setTotalPage(pagination.totalPage!!)
        .build()
}