package ru.sbertroika.tkp3.stop.list.gate.output.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import ru.sbertroika.stop.list.model.db.EmvStopList
import java.util.*

interface EmvStopListRepository : CoroutineCrudRepository<EmvStopList, UUID> {

    @Query("SELECT * FROM emv_stop_list WHERE project_id = :projectId and version > :version order by version asc limit 5")
    suspend fun findLast(projectId: UUID, version: Long): List<EmvStopList>

    @Query("SELECT * FROM emv_stop_list_view WHERE project_id = :projectId")
    suspend fun findMax(projectId: UUID): List<EmvStopList>
}