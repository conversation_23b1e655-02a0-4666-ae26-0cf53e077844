package ru.sbertroika.tkp3.stop.list.gate.service

import arrow.core.Either
import ru.sbertroika.common.stop.list.StopListType
import ru.sbertroika.common.stop.list.StopListUpdate
import ru.sbertroika.tkp3.stop.list.gate.model.StopListJsonModel
import java.util.*

interface StopListService {

    suspend fun findStopListUpdate(projectId: UUID, sType: StopListType, version: Long): Either<Throwable, StopListUpdate>
    suspend fun getVirtStopList(version: Long): Either<Throwable, StopListJsonModel>
}