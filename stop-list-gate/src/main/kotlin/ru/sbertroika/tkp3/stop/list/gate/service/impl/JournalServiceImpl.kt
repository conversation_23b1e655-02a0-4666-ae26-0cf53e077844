package ru.sbertroika.tkp3.stop.list.gate.service.impl

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.reactor.awaitSingle
import kotlinx.coroutines.withContext
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.data.domain.Sort
import org.springframework.data.r2dbc.core.R2dbcEntityOperations
import org.springframework.data.relational.core.query.Criteria
import org.springframework.data.relational.core.query.Query
import org.springframework.stereotype.Service
import ru.sbertroika.common.Pagination
import ru.sbertroika.tkp3.stop.list.gate.model.JournalFilter
import ru.sbertroika.tkp3.stop.list.gate.model.JournalResult
import ru.sbertroika.tkp3.stop.list.gate.output.model.StopListJournal
import ru.sbertroika.tkp3.stop.list.gate.service.JournalService
import java.math.BigInteger

@Service
class JournalServiceImpl(
    @Qualifier("clickhouseR2dbcEntityOperations")
    private val entityTemplate: R2dbcEntityOperations
) : JournalService {

    override suspend fun journalList(filter: JournalFilter, pagination: Pagination): Either<Error, JournalResult> = try {
        val search = mutableListOf<Criteria>()
/*
    TODO: search by pan
    filter.cardNum?.let {
            if (it.isNotEmpty()) search.add(Criteria.where("cardNum").`is`(it))
        }*/

        if (filter.type.isNotEmpty()) {
            if (filter.type.size == 1) {
                search.add(Criteria.where("type").`is`(filter.type.first().name))
            } else {
                search.add(Criteria.where("type").`in`(filter.type))
            }
        }

        filter.hash?.let {
            if (it.isNotEmpty()) search.add(Criteria.where("hash").`is`(it))
        }
        filter.hashType?.let {
            search.add(Criteria.where("hashType").`is`(it.name))
        }
        filter.uid?.let {
            if (it > BigInteger.ZERO) search.add(Criteria.where("uid").`is`(it))
        }
        filter.createVer?.let {
            if (it > 0) search.add(Criteria.where("createVer").`is`(it))
        }
        filter.createdAtFrom?.let {
            search.add(Criteria.where("createdAt").greaterThanOrEquals(it))
        }
        filter.createdAtTo?.let {
            search.add(Criteria.where("createdAt").lessThan(it))
        }

        val count = withContext(Dispatchers.IO) {
            entityTemplate.select(StopListJournal::class.java)
                .matching(Query.query(Criteria.from(search)))
                .count().block()
        }?.toInt()!!

        val res = withContext(Dispatchers.IO) {
            entityTemplate.select(StopListJournal::class.java)
                .matching(
                    Query.query(Criteria.from(search))
                        .sort(Sort.by("createdAt").descending())
                        .offset((pagination.limit * pagination.page).toLong())
                        .limit(pagination.limit)
                ).all()
                .collectList()
                .awaitSingle()
        }

        JournalResult(
            result = res!!,
            pagination = pagination.copy(
                totalCount = count,
                totalPage = if (count % pagination.limit == 0) count / pagination.limit else count / pagination.limit + 1
            )
        ).right()
    } catch (e: Exception) {
        Error(e).left()
    }
}