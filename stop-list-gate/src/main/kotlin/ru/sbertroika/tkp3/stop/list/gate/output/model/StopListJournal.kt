package ru.sbertroika.tkp3.stop.list.gate.output.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import ru.sbertroika.stop.list.gate.v1.OperationType
import java.time.ZonedDateTime

@Table("stop_list_journal")
class StopListJournal(

    /**
     * Идентификатор проекта
     */
    @Column("project_id")
    var projectId: String? = null,

    /**
     * Тип события
     */
    @Column("operation")
    var operation: OperationType? = null,

    /**
     * Время формирования транзакции на сервере
     */
    @Column("record_at")
    var recordAt: ZonedDateTime? = null,

    /**
     * Время создания записи в стоп-листе
     */
    @Column("created_at")
    var createdAt: ZonedDateTime? = null,

    /**
     * Время удаления записи из стоп-листа
     */
    @Column("deleted_at")
    var deletedAt: ZonedDateTime? = null,

    /**
     * Идентификатор носителя
     */
    @Column("carrier_id")
    var carrierId: String? = null,

    /**
     * Уникальный 8 байтный идентификатор в стоп-листе
     */
    @Column("uid")
    var uid: java.math.BigInteger? = null,

    /**
     * Хэш значение от номера носителя
     */
    @Column("hash")
    var hash: String? = null,

    /**
     * Тип хэш функции
     */
    @Column("hash_type")
    var hashType: HashType? = null,

    /**
     * Номер версии в которой носитель был добавлен в стоп-лист
     */
    @Column("create_ver")
    var createVer: Long? = null,

    /**
     * Номер версии в которой носитель был изъят из стоп-листа
     */
    @Column("delete_ver")
    var deleteVer: Long? = null,

    /**
     * Номер версии в которой носитель был изъят из стоп-листа
     */
    @Column("type")
    var type: StopListType? = null,
)

enum class HashType {
    SHA256, PAR, HMAC_SHA1, HMAC_SHA256, HMAC_SHA256_SHA256, STRIBOK512, BIN
}

enum class StopListType {
    EMV, TROIKA, PROSTOR
}