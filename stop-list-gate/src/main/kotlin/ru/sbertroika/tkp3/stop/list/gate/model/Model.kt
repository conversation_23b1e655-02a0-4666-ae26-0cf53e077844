package ru.sbertroika.tkp3.stop.list.gate.model

import com.fasterxml.jackson.annotation.JsonProperty
import ru.sbertroika.common.Pagination
import ru.sbertroika.common.stop.list.StopList
import ru.sbertroika.common.stop.list.StopListSubType
import ru.sbertroika.common.stop.list.StopListType
import ru.sbertroika.tkp3.stop.list.gate.output.model.StopListJournal
import java.io.Serializable
import java.math.BigInteger
import java.time.ZonedDateTime

data class StopListUpdate(
    val version: Long,
    val type: StopListType,
    val list: List<StopList>
)

data class EmvStopListUpdate(
    val version: Long,
    val isFull: Boolean,
    val url: String,
    val crc: String,
    val type: StopListSubType
)

data class JournalFilter(
    val cardNum: String? = null,
    val type: List<StopListType> = emptyList(),
    val createdAtFrom: ZonedDateTime? = null,
    val createdAtTo: ZonedDateTime? = null,
    val hash: String? = null,
    val hashType: StopListSubType? = null,
    val uid: BigInteger? = null,
    val createVer: Long? = null
)

data class JournalResult(
    val result: List<StopListJournal>,
    val pagination: Pagination
)

data class StopListJsonModel(
    val data: StopListJson
)

data class StopListJson(
    @JsonProperty("Version")
    val version: Long,
    @JsonProperty("MaxVersion")
    val maxVersion: Long,
    @JsonProperty("StopListName")
    val stopListName: List<String>,
    @JsonProperty("StopList")
    val stopList: List<List<*>>,
)