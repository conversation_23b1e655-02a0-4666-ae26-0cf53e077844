package ru.sbertroika.tkp3.stop.list.gate.input

import org.lognet.springboot.grpc.GRpcService
import ru.sbertroika.common.ServiceError
import ru.sbertroika.common.toOperationError
import ru.sbertroika.stop.list.gate.v1.StopListGateServiceGrpcKt
import ru.sbertroika.stop.list.gate.v1.StopListUpdateRequest
import ru.sbertroika.stop.list.gate.v1.StopListUpdateResponse
import ru.sbertroika.stop.list.gate.v1.stopListUpdateResponse
import ru.sbertroika.tkp3.stop.list.gate.service.StopListService
import java.util.*

@GRpcService
class StopListGateServiceGrpc(
    private val stopListService: StopListService
) : StopListGateServiceGrpcKt.StopListGateServiceCoroutineImplBase() {

    override suspend fun getStopListUpdate(request: StopListUpdateRequest): StopListUpdateResponse {
        return stopListService.findStopListUpdate(UUID.fromString(request.projectId), request.type, request.version).fold(
            {
                stopListUpdateResponse {
                    toOperationError(ServiceError(it.message))
                }
            },
            { update ->
                stopListUpdateResponse {
                    result = update
                }
            }
        )
    }
}