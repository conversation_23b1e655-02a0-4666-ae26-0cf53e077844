spring:
  application:
    name: stop-list-gate
  main:
    allow-bean-definition-overriding: true

  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:9092;localhost:9093}
    listener:
      ack-mode: manual
    consumer:
      enable-auto-commit: false
      properties:
        spring:
          json:
            trusted:
              packages: "*"
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer

  r2dbc:
    url: r2dbc:pool:${R2DB_URL:postgresql://postgres:postgres@localhost:5432/stoplist}
  clickhouse:
    url: r2dbc:clickhouse:${CLICKHOUSE_URL:http://click-0.tkp2.prod:8123/dev}

input_log:
  enabled: ${INPUT_LOG_ENABLED:true}

grpc:
  port: 5000
server:
  port: 8080


s3:
  url: ${S3_URL}
  access_key_id: ${S3_ACCESS_KEY_ID}
  secret_access_key: ${S3_SECRET_ACCESS_KEY}
  region: ${S3_REGION:ru-moscow}
  bucket: ${S3_BUCKET:tkp3-sl}

base_url: ${BASE_URL}