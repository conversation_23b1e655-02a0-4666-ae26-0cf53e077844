package ru.sbertroika.stop.list.model.db

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*


@Table("virt_stop_list")
data class VirtStopList(
    @Id
    @Column("id")
    var id: UUID? = null,

    @Column("project_id")
    var projectId: UUID? = null,

    @Column("created_at")
    var createdAt: Timestamp? = null,

    @Column("version")
    var version: Long? = null,

    @Column("full_filename")
    var fullFileName: String? = null,

    @Column("full_crc32")
    var fullCrc32: String? = null,

    @Column("full_size")
    var fullSize: String? = null,

    @Column("diff_filename")
    var diffFileName: String? = null,

    @Column("diff_crc32")
    var diffCrc32: String? = null,

    @Column("diff_size")
    var diffSize: Long? = null
)