package ru.sbertroika.stop.list.model.db

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*

@Table("virt_stop_list_record")
data class VirtStopListRecord(

    @Id
    @Column("id")
    var id: UUID? = null,

    @Column("project_id")
    var projectId: UUID? = null,

    @Column("created_at")
    var createdAt: Timestamp? = null,

    @Column("version")
    var version: Long? = null,

    @Column("carrier_id")
    var carrierId: UUID? = null,

    @Column("uid")
    var uid: String? = null
)