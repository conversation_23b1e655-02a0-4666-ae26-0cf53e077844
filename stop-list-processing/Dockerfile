FROM gradle:8.13-jdk17-alpine as builder
ARG GRADLE_USER_HOME=/tmp/.gradle
ENV GRADLE_USER_HOME=$GRADLE_USER_HOME
RUN apk add gcompat
WORKDIR /build
ADD . /build
COPY .ci-gradle/gradle.properties /tmp/.gradle/gradle.properties
COPY .ci-gradle/gradle.properties /home/<USER>/.gradle/gradle.properties
COPY .ci-gradle/gradle.properties /root/.gradle/gradle.properties

RUN env

RUN gradle --no-daemon stop-list-processing:bootJar -i


FROM openjdk:17-alpine

RUN apk add --no-cache libstdc++

COPY --from=builder build/stop-list-processing/build/libs/stop-list-processing-*.jar ./stop-list-processing.jar
COPY ./lib-stop-list/lib/* /opt/libs/

ENV LD_LIBRARY_PATH=/opt/libs

EXPOSE 8080
EXPOSE 6000

ENTRYPOINT ["java", "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:6000", "-Djava.library.path=/opt/libs", "-Djava.security.egd=file:/dev/./urandom", "-jar", "stop-list-processing.jar"]