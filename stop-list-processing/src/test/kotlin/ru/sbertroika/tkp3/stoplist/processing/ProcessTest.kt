package ru.sbertroika.tkp3.stoplist.processing

import org.junit.jupiter.api.Test
import ru.sbertroika.stoplist.StoplistTools
import ru.sbertroika.tkp3.stoplist.processing.util.cardUidToDec
import ru.sbertroika.tkp3.stoplist.processing.util.hexToByteArray
import ru.sbertroika.tkp3.stoplist.processing.util.toHexString
import java.io.File
import java.nio.ByteBuffer
import java.nio.charset.StandardCharsets
import java.security.MessageDigest

class ProcessTest {

    @Test
    fun test() {
        val pans = listOf(
            "2202241891810949",
            "2202246397529570",
            "****************",
            "****************",
            "****************",
        )

        val f = File("st.bin")
        val buffer = ByteBuffer.allocate(pans.size * 40)
        val digest = MessageDigest.getInstance("SHA-256")
        val mapPans = mutableMapOf<ULong, String>()
        val map = pans.map { pan ->
            val sha256 = digest.digest(pan.toByteArray(StandardCharsets.UTF_8))
            val idx = StoplistTools.getUniqueIndex(sha256.toHexString())!!.toULong()
            mapPans[idx] = pan
            Pair(idx, sha256)
        }.toMap().toSortedMap(compareBy{it})
        map.forEach {
            println("${mapPans[it.key]};${it.key};${it.value.toHexString()}")
            buffer.putLong(it.key.toLong()).put(it.value)
        }
        f.writeBytes(buffer.array())
    }

    @Test
    fun test2() {
        val pans = listOf(
            "34119B9962F3E6",
            "048C69D2D05F80",
            "05853F771D8100",
            "1D67D42C000001",
            "EF819E3E",
            "FFFFFFFFFFFFFFFF",
        )
        val f = File("mfst.bin")
        val buffer = ByteBuffer.allocate(pans.size * 8)
        val mapPans = mutableMapOf<ULong, String>()
        val uids = pans.map {
            val idx = it.padEnd(16, '0').hexToByteArray().cardUidToDec().toULong()
            mapPans[idx] = it
            idx
        }.toSet().sortedBy { it }
        mapPans.forEach {
            println("${it.value};${it.key}")
            buffer.putLong(it.key.toLong())
        }
        f.writeBytes(buffer.array())
    }
}