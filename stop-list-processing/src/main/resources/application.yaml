spring:
  application:
    name: stop-list-processing
  main:
    allow-bean-definition-overriding: true

  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:9092;localhost:9093}
    listener:
      ack-mode: manual
    consumer:
      enable-auto-commit: false
      properties:
        spring:
          json:
            trusted:
              packages: "*"
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    emv_stop_list_in_topic: ${SL_IN_TOPIC:PRO.SL.EMV}
    virt_stop_list_in_topic: ${VIRT_SL_IN_TOPIC:PRO.SL.VIRT}
    stop_list_error_out_topic: ${SL_ERROR_OUT_TOPIC:SL.ERROR.OUT}
    stop_list_journal_topic: ${SL_JOURNAL_TOPIC:SL.JOURNAL.OUT}
    emv_stop_list_processing_in_group: ${SL_PROCESSING_IN_GROUP:emv_stop_list_processing_in_group}
    virt_stop_list_processing_in_group: ${VIRT_SL_PROCESSING_IN_GROUP:virt_stop_list_processing_in_group}

  r2dbc:
    url: r2dbc:pool:${R2DB_URL:postgresql://postgres:postgres@localhost:5432/stoplist}
    username: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}

  flyway:
    enabled: ${DB_MIGRATION_ENABLE:true}
    validate-migration-naming: true
    url: jdbc:${DB_URL:postgresql://localhost:5432/stoplist}
    user: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}
    locations:
      - classpath:db/migration
    mixed: true
    baseline-on-migrate: true

server:
  port: 8080

emv_emission_url: ${EMV_EMISSION_URL:localhost:5009}
qr_emission_url: ${QR_EMISSION_URL:localhost:5006}

s3:
  url: ${S3_URL:localhost:8080}
  access_key_id: ${S3_ACCESS_KEY_ID:fksdjfksd}
  secret_access_key: ${S3_SECRET_ACCESS_KEY:fsdfklsd}
  region: ${S3_REGION:ru-moscow}
  bucket: ${S3_BUCKET:tkp3-sl}

tmp_path: ${TMP_PATH:/tmp}
diff_enable: ${DIFF_ENABLE:false}

develop:
  emission_stub: ${DEVELOP_EMISSION_STUB:false}
  s3_disable_checksum: ${DEVELOP_S3_DISABLE_CHECKSUM:false}
  stoplist_lib_enable_log: ${DEVELOP_STOPLIST_LIB_ENABLE_LOG:true}