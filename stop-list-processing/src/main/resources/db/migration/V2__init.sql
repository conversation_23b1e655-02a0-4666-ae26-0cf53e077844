create table emv_stop_list
(
    id            uuid primary key default gen_random_uuid() not null,
    created_at    TIMESTAMP        default now()             not null,
    project_id    uuid                                       not null,
    hash_type     smallint                                   not null,
    version       bigint                                     not null,
    full_filename varchar(300)                               not null,
    full_crc32    varchar(20)                                not null,
    diff_filename varchar(300)                               null,
    diff_crc32    varchar(20)                                null
);
CREATE INDEX idx_emv_stop_list_by_hashtype_and_version ON emv_stop_list (project_id, hash_type, version);

create table emv_stop_list_record
(
    id         uuid primary key default gen_random_uuid() not null,
    created_at TIMESTAMP        default now()             not null,
    project_id uuid                                       not null,
    carrier_id uuid                                       not null,
    uid        varchar(64)                                not null,
    hash       varchar(128)                               not null,
    hash_type  smallint                                   not null,
    version    bigint                                     not null
);

CREATE INDEX idx_emv_stop_list_record_by_hashtype_and_uid ON emv_stop_list_record (project_id, hash_type, uid ASC);
CREATE INDEX idx_emv_stop_list_record_by_carrier_id ON emv_stop_list_record (carrier_id);
