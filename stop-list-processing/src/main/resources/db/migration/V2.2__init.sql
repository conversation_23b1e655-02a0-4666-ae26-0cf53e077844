create table virt_stop_list
(
    id            uuid primary key default gen_random_uuid() not null,
    created_at    TIMESTAMP        default now()             not null,
    project_id    uuid                                       not null,
    version       bigint                                     not null,
    full_filename varchar(300)                               null,
    full_crc32    varchar(20)                                null,
    full_size     bigint                                     null,
    diff_filename varchar(300)                               null,
    diff_crc32    varchar(20)                                null,
    diff_size     bigint                                     null
);

CREATE INDEX idx_virt_stop_list_by_project_and_version ON virt_stop_list (project_id, version);

create table virt_stop_list_record
(
    id            uuid primary key default gen_random_uuid() not null,
    created_at    TIMESTAMP        default now()             not null,
    project_id    uuid                                       not null,
    carrier_id    uuid                                       not null,
    uid           varchar(64)                                not null,
    version       bigint                                     not null
);
CREATE INDEX idx_virt_stop_list_record_by_project_and_version ON virt_stop_list_record (project_id, version);
CREATE INDEX idx_virt_stop_list_record_by_project_and_uid ON virt_stop_list_record (project_id, uid ASC);
CREATE INDEX idx_virt_stop_list_record_by_carrier_id ON virt_stop_list_record (carrier_id);

create unique index idx_virt_stop_list_hash_type_version_unq on virt_stop_list (version);

ALTER TABLE emv_stop_list ADD full_size  bigint;
ALTER TABLE emv_stop_list ADD diff_size  bigint;