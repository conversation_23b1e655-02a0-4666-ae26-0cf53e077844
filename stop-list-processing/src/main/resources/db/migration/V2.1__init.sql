create unique index idx_emv_stop_list_hash_type_version_unq on emv_stop_list (hash_type, version);

create view emv_stop_list_view as
select t.*
from (select distinct on (hash_type) hash_type,
                                     last_value(version) over wnd as version
      from emv_stop_list
      window wnd as (
              partition by hash_type order by version asc
              rows between unbounded preceding and unbounded following
              )) a
         join emv_stop_list t on (a.hash_type = t.hash_type and a.version = t.version);