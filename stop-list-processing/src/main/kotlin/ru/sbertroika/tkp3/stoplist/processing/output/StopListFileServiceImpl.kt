package ru.sbertroika.tkp3.stoplist.processing.output

import arrow.core.Either
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import ru.sbertroika.stop.list.model.db.EmvStopListRecord
import ru.sbertroika.stoplist.Stoplist
import ru.sbertroika.stoplist.StoplistElementSize
import ru.sbertroika.tkp3.stoplist.api.model.HashType
import ru.sbertroika.tkp3.stoplist.processing.model.EmvStopListFile
import ru.sbertroika.tkp3.stoplist.processing.model.FileSizeZeroError
import ru.sbertroika.tkp3.stoplist.processing.util.hexToByteArray
import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.GetObjectRequest
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import java.io.File
import java.nio.ByteBuffer
import java.util.zip.CRC32

@Service
class StopListFileServiceImpl(
    private val s3Client: S3Client,
    private val stoplistLib: Stoplist,

    @Value("\${s3.bucket}")
    val bucket: String,
    @Value("\${tmp_path}")
    val tmpPath: String
) : StopListFileService {

    override suspend fun makeEmvStopListFileFull(hashType: HashType, list: List<EmvStopListRecord>, version: Long): Either<Throwable, EmvStopListFile> = Either.catch {
        val fileName = "${hashType.name}_${version}_full.bin"

        val recordSize = when(hashType) {
            HashType.SHA256 -> 40
            HashType.PAR -> 37
            HashType.HMAC_SHA1 -> 28
            HashType.BIN -> 8
            HashType.HMAC_SHA256 -> 40
            HashType.HMAC_SHA256_SHA256 -> 40
            HashType.STRIBOK512 -> 520
        }

        val buffer = ByteBuffer.allocate(list.size * recordSize)
        if (hashType == HashType.BIN) {
            for (record in list.sortedBy { it.hash!!.toULong() }) {
                buffer.put(long2Bytes(record.hash!!.toLong()))
            }
        } else {
            for (record in list.sortedBy { it.uid!!.toULong() }) {
                buffer.put(long2Bytes(record.uid!!.toULong().toLong()))
                    .put(record.hash!!.hexToByteArray())
            }
        }
        val crc = CRC32()
        crc.update(buffer.array())
        val crc32 = java.lang.Long.toHexString(crc.value).uppercase()

        s3Client.putObject(
            PutObjectRequest.builder()
                .bucket(bucket)
                .key(fileName)
                .checksumCRC32(crc32)
                .build(),
            RequestBody.fromByteBuffer(buffer)
        )

        buffer.clear()

        EmvStopListFile(
            fileName = fileName,
            crc32 = crc32
        )
    }

    override suspend fun makeEmvStopListFileDiff(hashType: HashType, newVersionFileName: String, oldVersionFileName: String, version: Long): Either<Throwable, EmvStopListFile> = Either.catch {
        val diffFileName = "${hashType.name}_${version}_diff.bin"

        val newFile = s3Client.getObject(
            GetObjectRequest.builder()
                .bucket(bucket)
                .key(newVersionFileName)
                .build()
        )

        val newFilePath = File(tmpPath, newVersionFileName)
        newFilePath.writeBytes(withContext(Dispatchers.IO) {
            newFile.readAllBytes()
        })

        val oldFile = s3Client.getObject(
            GetObjectRequest.builder()
                .bucket(bucket)
                .key(oldVersionFileName)
                .build()
        )
        val oldFilePath = File(tmpPath, oldVersionFileName)
        oldFilePath.writeBytes(withContext(Dispatchers.IO) {
            oldFile.readAllBytes()
        })

        if (newFilePath.length() == 0L || oldFilePath.length() == 0L) throw FileSizeZeroError()

        try {
            val diffFilePath = File(tmpPath, diffFileName)
            stoplistLib.generateDiff(
                pathToOldFile = oldFilePath.absolutePath,
                pathToNewFile = newFilePath.absolutePath,
                pathToDiffFile = diffFilePath.absolutePath,
                elementSize = when(hashType) {
                    HashType.SHA256 -> StoplistElementSize.SL_PAN_SHA256
                    HashType.PAR -> StoplistElementSize.SL_PAR
                    HashType.HMAC_SHA1 -> StoplistElementSize.SL_HMAC_SHA1
                    HashType.BIN -> StoplistElementSize.SL_BIN
                    HashType.HMAC_SHA256 -> StoplistElementSize.SL_HMAC_SHA256
                    HashType.HMAC_SHA256_SHA256 -> StoplistElementSize.SL_HMAC_SHA256_SHA256
                    HashType.STRIBOK512 -> StoplistElementSize.SL_STRIBOG512
                }
            )

            val crc = CRC32()
            crc.update(diffFilePath.readBytes())
            val crc32 = java.lang.Long.toHexString(crc.value).uppercase()

            s3Client.putObject(
                PutObjectRequest.builder()
                    .bucket(bucket)
                    .key(diffFileName)
                    .checksumCRC32(crc32)
                    .build(),
                RequestBody.fromFile(diffFilePath)
            )

            newFilePath.delete()
            oldFilePath.delete()
            diffFilePath.delete()

            EmvStopListFile(
                fileName = diffFileName,
                crc32 = crc32
            )
        } catch (e: Throwable) {
            throw Error(e)
        }
    }

    fun long2Bytes(value: Long, lts: Boolean = false): ByteArray {
        val b0 = value and 0xFF
        val b1 = value ushr 8 and 0xFF
        val b2 = value ushr 16 and 0xFF
        val b3 = value ushr 24 and 0xFF
        val b4 = value ushr 32 and 0xFF
        val b5 = value ushr 40 and 0xFF
        val b6 = value ushr 48 and 0xFF
        val b7 = value ushr 56 and 0xFF

        val src = byteArrayOf(
            b0.toByte(), b1.toByte(), b2.toByte(), b3.toByte(),
            b4.toByte(), b5.toByte(), b6.toByte(), b7.toByte()
        )

        if (lts) {
            src.reverse()
        }

        return src
    }
}