package ru.sbertroika.tkp3.stoplist.processing.output.db

import org.springframework.data.domain.Sort
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import ru.sbertroika.stop.list.model.db.EmvStopListRecord
import java.util.*

interface EmvStopListRecordRepository : CoroutineCrudRepository<EmvStopListRecord, UUID> {

    suspend fun findByProjectIdAndHashTypeAndHash(projectId: UUID, hashType: Int, hash: String): EmvStopListRecord?

    suspend fun findAllByProjectIdAndHashType(projectId: UUID, hashType: Int, sort: Sort): List<EmvStopListRecord>

    suspend fun deleteAllByProjectId(projectId: UUID)
}