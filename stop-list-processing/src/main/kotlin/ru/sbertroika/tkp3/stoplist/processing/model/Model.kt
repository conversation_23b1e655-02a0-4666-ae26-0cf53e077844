package ru.sbertroika.tkp3.stoplist.processing.model

import com.fasterxml.jackson.annotation.JsonFormat
import ru.sbertroika.stoplist.StoplistTools
import ru.sbertroika.tkp3.stoplist.api.model.HashType
import ru.sbertroika.tkp3.stoplist.api.model.StopListOperationType
import java.time.ZonedDateTime
import java.util.*

data class EmvStopListRecord(
    val projectId: UUID?,
    val type: StopListOperationType,
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    val recordAt: ZonedDateTime?,
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    val createdAt: ZonedDateTime?,
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    val deletedAt: ZonedDateTime?,
    val carrierId: UUID?,
    val uid: ULong,
    val hash: String,
    val hashType: HashType,
    val createVersion: Long?,
    val deleteVersion: Long?,
)

data class EmvStopListFile(
    val fileName: String,
    val crc32: String
)

data class VirtStopListRecord(
    val projectId: UUID?,
    val type: StopListOperationType,
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    val recordAt: ZonedDateTime?,
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    val createdAt: ZonedDateTime?,
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    val deletedAt: ZonedDateTime?,
    val carrierId: UUID?,
    val uid: String,
    val createVersion: Long?,
    val deleteVersion: Long?,
)

class FileSizeZeroError : Error()

data class KafkaStopListRecord(
    val projectId: UUID?,
    val type: StopListOperationType,
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    val recordAt: ZonedDateTime?,
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    val createdAt: ZonedDateTime?,
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "UTC")
    val deletedAt: ZonedDateTime?,
    val carrierId: UUID?,
    val uid: ULong,
    val hash: String?,
    val hashType: HashType?,
    val createVersion: Long?,
    val deleteVersion: Long?,
    val typeStopList: StopListRecordType
) {
    companion object {
        fun kafkaFromEMV(record: EmvStopListRecord) : KafkaStopListRecord {
            return KafkaStopListRecord(
                projectId = record.projectId,
                type = record.type,
                recordAt = record.recordAt,
                createdAt = record.createdAt,
                deletedAt = record.deletedAt,
                carrierId = record.carrierId,
                uid = record.uid,
                createVersion = record.createVersion,
                deleteVersion = record.deleteVersion,
                typeStopList = StopListRecordType.EMV,
                hash = record.hash,
                hashType = record.hashType
            )
        }
        fun kafkaFromVirt(record: VirtStopListRecord) : KafkaStopListRecord {
            return KafkaStopListRecord(
                projectId = record.projectId,
                type = record.type,
                recordAt = record.recordAt,
                createdAt = record.createdAt,
                deletedAt = record.deletedAt,
                carrierId = record.carrierId,
                uid = record.uid.toULong(),
                createVersion = record.createVersion,
                deleteVersion = record.deleteVersion,
                typeStopList = StopListRecordType.VIRT,
                hash = null,
                hashType = null
            )
        }
    }
}

enum class StopListRecordType {
    EMV,
    VIRT
}