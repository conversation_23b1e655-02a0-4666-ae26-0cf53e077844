package ru.sbertroika.tkp3.stoplist.processing.config

import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import ru.sbertroika.stoplist.Stoplist

@Configuration
open class StoplistLibConfig(
    @Value("\${develop.stoplist_lib_enable_log}")
    val stopListLibEnableLog: Boolean
) {

    @Bean
    open fun stoplist(): Stoplist {
        return Stoplist().apply {
            if (stopListLibEnableLog) {
                initLib("", "0123456789abcdef", true, 5)
            }
        }
    }
}