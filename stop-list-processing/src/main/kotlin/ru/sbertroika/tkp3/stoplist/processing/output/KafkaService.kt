package ru.sbertroika.tkp3.stoplist.processing.output

import arrow.core.Either
import ru.sbertroika.tkp3.stoplist.processing.model.EmvStopListRecord
import ru.sbertroika.tkp3.stoplist.processing.model.VirtStopListRecord

interface KafkaService {

    suspend fun sendEmvStopListRecords(list: List<EmvStopListRecord>): Either<Throwable, Unit>
    suspend fun sendVirtStopListRecords(list: List<VirtStopListRecord>): Either<Throwable, Unit>
}