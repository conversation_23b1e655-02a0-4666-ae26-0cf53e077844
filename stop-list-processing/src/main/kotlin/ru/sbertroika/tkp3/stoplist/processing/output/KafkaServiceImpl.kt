package ru.sbertroika.tkp3.stoplist.processing.output

import arrow.core.Either
import org.apache.kafka.clients.producer.ProducerRecord
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.ProducerFactory
import org.springframework.stereotype.Service
import ru.sbertroika.tkp3.stoplist.processing.model.EmvStopListRecord
import ru.sbertroika.tkp3.stoplist.processing.model.KafkaStopListRecord
import ru.sbertroika.tkp3.stoplist.processing.model.VirtStopListRecord
import ru.sbertroika.tkp3.stoplist.processing.util.mapper
import java.util.*

@Service
class KafkaServiceImpl(
    private val kafkaProducerFactory: ProducerFactory<String, Any>,

    @Value("\${spring.kafka.stop_list_journal_topic}")
    val journalTopic: String,
): KafkaService {

    private val mapperNoExtensions = mapper()
    private val producer = kafkaProducerFactory.createProducer()

    override suspend fun sendEmvStopListRecords(list: List<EmvStopListRecord>): Either<Throwable, Unit> = Either.catch {
        list.forEach {
            val out = ProducerRecord<String, Any>(
                journalTopic,
                UUID.randomUUID().toString(),
                mapperNoExtensions.writeValueAsString(KafkaStopListRecord.kafkaFromEMV(it))
            )
            producer.send(out)
        }
    }

    override suspend fun sendVirtStopListRecords(list: List<VirtStopListRecord>): Either<Throwable, Unit> = Either.catch {
        list.forEach {
            val out = ProducerRecord<String, Any>(
                journalTopic,
                UUID.randomUUID().toString(),
                mapperNoExtensions.writeValueAsString(KafkaStopListRecord.kafkaFromVirt(it))
            )
            producer.send(out)
        }
    }
}