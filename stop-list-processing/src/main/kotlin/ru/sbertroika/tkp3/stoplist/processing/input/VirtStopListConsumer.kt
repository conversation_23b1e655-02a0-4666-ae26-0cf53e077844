package ru.sbertroika.tkp3.stoplist.processing.input

import arrow.core.Either
import arrow.core.right
import com.fasterxml.jackson.module.kotlin.readValue
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.slf4j.LoggerFactory
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.kafka.support.Acknowledgment
import org.springframework.stereotype.Component
import ru.sbertroika.stop.list.model.db.VirtStopList
import ru.sbertroika.tkp3.stoplist.api.model.StopListOperationType
import ru.sbertroika.tkp3.stoplist.api.model.VirtualStopList
import ru.sbertroika.tkp3.stoplist.api.model.VirtualStopListOperationRecord
import ru.sbertroika.tkp3.stoplist.processing.model.VirtStopListRecord
import ru.sbertroika.tkp3.stoplist.processing.output.DBService
import ru.sbertroika.tkp3.stoplist.processing.output.KafkaService
import ru.sbertroika.tkp3.stoplist.processing.output.QREmissionService
import java.sql.Timestamp
import java.time.Duration
import java.time.Instant
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.*

@Component
class VirtStopListConsumer(
    val dbService: DBService,
    val qrEmissionService: QREmissionService,
    val kafkaService: KafkaService) {


    private val logger = LoggerFactory.getLogger(this::class.java)
    private val mapper = ru.sbertroika.tkp3.stoplist.processing.util.mapper()

    @KafkaListener(groupId = "\${spring.kafka.virt_stop_list_processing_in_group}", topics = ["\${spring.kafka.virt_stop_list_in_topic}"])
    fun receive(record: ConsumerRecord<String, String>, acknowledgment: Acknowledgment) = runBlocking {
        val stopList = mapper.readValue<VirtualStopList>(record.value())

        if (stopList.records.isEmpty()) {
            logger.warn("Empty records")
            acknowledgment.acknowledge()
            return@runBlocking
        }

        if (stopList.isReset) {
            dbService.resetVirtByProject(stopList.project).fold(
                {
                    logger.error("Error reset stop-list by projectId ${stopList.project}")
                    acknowledgment.acknowledge()
                    return@runBlocking
                }, {}
            )
        }

        val version = ZonedDateTime.now(ZoneId.of("UTC")).format(fmtVer).toLong()

        extract(stopList, version).fold(
            {
                acknowledgment.nack(Duration.ofMinutes(5))
                return@runBlocking
            },
            { records ->
                if (records.first.isNotEmpty() || records.second.isNotEmpty()) {
                    try {
                        if (dbService.insertVirtStopListRecords(records.first).isLeft()) {
                            logger.error("Error insert records")
                            acknowledgment.nack(Duration.ofMinutes(5))
                            return@runBlocking
                        }
                        val deleted = dbService.deleteAllByVersion(records.second).fold(
                            {
                                logger.error("Error delete records")
                                acknowledgment.nack(Duration.ofMinutes(5))
                                return@runBlocking
                            },
                            { it }
                        )
                        if (kafkaService.sendVirtStopListRecords(records.first).isLeft() || kafkaService.sendVirtStopListRecords(deleted).isLeft()) {
                            logger.error("Error send journal")
                            acknowledgment.nack(Duration.ofMinutes(5))
                            return@runBlocking
                        }

                        makeStopList(stopList, stopList.project, version).fold(
                            {
                                acknowledgment.nack(Duration.ofMinutes(5))
                            },
                            { }
                        )
                    } catch (e: Exception) {
                        logger.error("StopList processing DB operation error: ", e)
                        acknowledgment.nack(Duration.ofMinutes(5))
                        return@runBlocking
                    }
                }
            }
        )

    }

    private suspend fun extract(stopList: VirtualStopList, version: Long): Either<Throwable, Pair<MutableList<VirtStopListRecord>, MutableList<VirtStopListRecord>>> = Either.catch {
        val appendRecordsList = mutableListOf<VirtStopListRecord>()
        val deleteRecordsList = mutableListOf<VirtStopListRecord>()
        stopList.records.forEach { stopListRecord ->
            if (stopListRecord.type == StopListOperationType.APPEND) {
                qrEmissionService.emissionByNum(stopListRecord.num).fold(
                    { e ->
                        logger.error("emission failed ${stopListRecord.num}", e)
                        throw Error("Error emission")
                    },
                    { carrierId ->
                        appendRecordsList += toAppendRecord(stopListRecord, carrierId, version, stopList.project)
                    }
                )
            } else if (stopListRecord.type == StopListOperationType.DELETE) {
                deleteRecordsList += toDeleteRecord(stopListRecord, version, stopList.project)
            }
        }

        return Pair(appendRecordsList, deleteRecordsList).right()
    }

    private fun toAppendRecord(rec: VirtualStopListOperationRecord, carrierId: String, version: Long, projectId: UUID) = VirtStopListRecord(
        projectId = projectId,
        recordAt = ZonedDateTime.now(ZoneId.of("UTC")),
        carrierId = UUID.fromString(carrierId),
        createdAt = ZonedDateTime.now(ZoneId.of("UTC")),
        deletedAt = null,
        uid = rec.num,
        createVersion = version,
        deleteVersion = null,
        type = StopListOperationType.APPEND
    )

    private fun toDeleteRecord(rec: VirtualStopListOperationRecord, version: Long, projectId: UUID) = VirtStopListRecord(
        projectId = projectId,
        recordAt = ZonedDateTime.now(ZoneId.of("UTC")),
        carrierId = null, // null поля заполняются при удалении
        createdAt = null,
        uid = rec.num,
        createVersion = null,
        deleteVersion = version,
        deletedAt = ZonedDateTime.now(ZoneId.of("UTC")),
        type = StopListOperationType.DELETE
    )

    private suspend fun makeStopList(stopList: VirtualStopList, projectId: UUID, version: Long): Either<Throwable, Unit> = Either.catch {
        dbService.getVirtStopLists(projectId).fold(
            {
                logger.error("Error fetch stop list", it)
                throw Error(it)
            },
            { list ->

                dbService.insertVirtStopList(
                    VirtStopList(
                        projectId = stopList.project,
                        createdAt = Timestamp.from(Instant.now()),
                        version = version
                    )
                )
            }
        )
    }

    companion object {
        private val fmtVer = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS")
    }
}