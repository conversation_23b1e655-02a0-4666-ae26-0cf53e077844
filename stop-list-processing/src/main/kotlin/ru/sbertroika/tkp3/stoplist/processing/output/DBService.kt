package ru.sbertroika.tkp3.stoplist.processing.output

import arrow.core.Either
import ru.sbertroika.stop.list.model.db.EmvStopList
import ru.sbertroika.stop.list.model.db.VirtStopList
import ru.sbertroika.tkp3.stoplist.api.model.HashType
import java.util.*

interface DBService {

    suspend fun getEmvStopLists(hashType: HashType, projectId: UUID): Either<Throwable, List<ru.sbertroika.stop.list.model.db.EmvStopListRecord>>

    suspend fun getLastStopList(hashType: HashType, projectId: UUID): Either<Throwable, EmvStopList?>

    suspend fun saveEmvStopListRecord(record: ru.sbertroika.stop.list.model.db.EmvStopListRecord): Either<Throwable, ru.sbertroika.stop.list.model.db.EmvStopListRecord>

    suspend fun insertEmvStopListRecords(records: List<ru.sbertroika.tkp3.stoplist.processing.model.EmvStopListRecord>): Either<Throwable, List<ru.sbertroika.stop.list.model.db.EmvStopListRecord>>

    suspend fun deleteAllByVersionAndHash(records: List<ru.sbertroika.tkp3.stoplist.processing.model.EmvStopListRecord>): Either<Throwable, List<ru.sbertroika.tkp3.stoplist.processing.model.EmvStopListRecord>>

    suspend fun insertEmvStopList(sl: EmvStopList): Either<Throwable, EmvStopList>

    suspend fun resetByProject(projectId: UUID): Either<Throwable, Unit>

    suspend fun getVirtStopLists(projectId: UUID): Either<Throwable, List<ru.sbertroika.stop.list.model.db.VirtStopListRecord>>

    suspend fun getLastStopList(projectId: UUID): Either<Throwable, VirtStopList?>

    suspend fun saveVirtStopListRecord(record: ru.sbertroika.stop.list.model.db.VirtStopListRecord): Either<Throwable, ru.sbertroika.stop.list.model.db.VirtStopListRecord>

    suspend fun insertVirtStopListRecords(records: List<ru.sbertroika.tkp3.stoplist.processing.model.VirtStopListRecord>): Either<Throwable, List<ru.sbertroika.stop.list.model.db.VirtStopListRecord>>

    suspend fun deleteAllByVersion(records: List<ru.sbertroika.tkp3.stoplist.processing.model.VirtStopListRecord>): Either<Throwable, List<ru.sbertroika.tkp3.stoplist.processing.model.VirtStopListRecord>>

    suspend fun insertVirtStopList(sl: VirtStopList): Either<Throwable, VirtStopList>

    suspend fun resetVirtStopListByProject(projectId: UUID): Either<Throwable, Unit>
    suspend fun resetVirtByProject(projectId: UUID): Either<Throwable, Unit>
}