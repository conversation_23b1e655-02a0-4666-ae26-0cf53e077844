package ru.sbertroika.tkp3.stoplist.processing.output

import arrow.core.Either
import ru.sbertroika.stop.list.model.db.EmvStopListRecord
import ru.sbertroika.tkp3.stoplist.api.model.HashType
import ru.sbertroika.tkp3.stoplist.processing.model.EmvStopListFile

interface StopListFileService {

    suspend fun makeEmvStopListFileFull(hashType: HashType, list: List<EmvStopListRecord>, version: Long): Either<Throwable, EmvStopListFile>

    suspend fun makeEmvStopListFileDiff(hashType: HashType, newVersionFileName: String, oldVersionFileName: String, version: Long): Either<Throwable, EmvStopListFile>
}