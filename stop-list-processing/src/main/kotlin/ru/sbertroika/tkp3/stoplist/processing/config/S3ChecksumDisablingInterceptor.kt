package ru.sbertroika.tkp3.stoplist.processing.config

import software.amazon.awssdk.core.SdkRequest
import software.amazon.awssdk.core.interceptor.ExecutionAttributes
import software.amazon.awssdk.core.interceptor.ExecutionInterceptor
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import software.amazon.awssdk.core.interceptor.Context.ModifyRequest
import software.amazon.awssdk.services.s3.model.ChecksumAlgorithm

/**
 * Для локального запуска в docker
 * Перехватывает put запросы в S3 и убирает x-amz-checksum-* заголовки
 *
 * S3 minio не поддерживает алгоритм checksumCRC32 и при наличии заголовка x-amz-checksum-* проводит checksum в SHA256
 *  что приводит к ошибке из за разницы checksum
 */
class S3ChecksumDisablingInterceptor() : ExecutionInterceptor {

    override fun modifyRequest(context: ModifyRequest, executionAttributes: ExecutionAttributes): SdkRequest {
        val request = context.request()
        if (request is PutObjectRequest) {
            return request.toBuilder()
                .checksumCRC32(null)
                .checksumCRC32C(null)
                .checksumSHA1(null)
                .checksumSHA256(null)
                .checksumAlgorithm(null as ChecksumAlgorithm?)
                .build()
        }
        return request
    }
}