package ru.sbertroika.tkp3.stoplist.processing.stub

import arrow.core.Either
import arrow.core.right
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service
import ru.sbertroika.tkp3.stoplist.processing.output.EMVEmissionService
import java.util.*


@Service
@ConditionalOnProperty(name = ["develop.emission_stub"], havingValue = "true")
class EMVEmissionServiceStubImpl() : EMVEmissionService {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override suspend fun emissionBySha256(hash: String): Either<Error, String> =
        UUID.randomUUID().toString().right().also { logger.info("stub emissionBySha256") }

    override suspend fun emissionByPar(hash: String): Either<Error, String> =
        UUID.randomUUID().toString().right().also { logger.info("stub emissionByPar") }
}
