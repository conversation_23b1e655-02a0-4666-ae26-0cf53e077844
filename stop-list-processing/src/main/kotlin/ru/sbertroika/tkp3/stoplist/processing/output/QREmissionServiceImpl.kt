package ru.sbertroika.tkp3.stoplist.processing.output

import arrow.core.Either
import arrow.core.left
import io.grpc.ManagedChannelBuilder
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import ru.sbertroika.qr.emission.v1.EmissionRequest
import ru.sbertroika.qr.emission.v1.QREmissionServiceGrpcKt

@Service
class QREmissionServiceImpl(
    @Value("\${qr_emission_url}")
    private val qrEmissionServiceUrl: String
) : QREmissionService {

    private final val channel = ManagedChannelBuilder.forTarget(qrEmissionServiceUrl)
        .usePlaintext()
        .enableRetry()
        .maxRetryAttempts(3)
        .build()
    val client = QREmissionServiceGrpcKt.QREmissionServiceCoroutineStub(channel)
    override suspend fun emissionByNum(num: String): Either<Error, String> {
        try {
            val request = EmissionRequest.newBuilder()
                .setNumber(num)
                .build()
            val response = client.emission(request)
            if (response.hasError())
                return Error(response.error.message).left()
            return Either.Right(response.card.id)
        } catch (e: Exception) {
            return Either.Left(Error(e))
        }
    }
}