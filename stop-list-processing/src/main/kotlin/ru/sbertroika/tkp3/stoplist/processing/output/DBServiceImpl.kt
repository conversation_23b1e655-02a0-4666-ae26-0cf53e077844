package ru.sbertroika.tkp3.stoplist.processing.output

import arrow.core.Either
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service
import ru.sbertroika.stop.list.model.db.EmvStopList
import ru.sbertroika.stop.list.model.db.VirtStopList
import ru.sbertroika.stop.list.model.db.VirtStopListRecord
import ru.sbertroika.tkp3.stoplist.api.model.HashType
import ru.sbertroika.tkp3.stoplist.processing.output.db.EmvStopListRecordRepository
import ru.sbertroika.tkp3.stoplist.processing.output.db.EmvStopListRepository
import ru.sbertroika.tkp3.stoplist.processing.output.db.VirtStopListRecordRepository
import ru.sbertroika.tkp3.stoplist.processing.output.db.VirtStopListRepository
import java.sql.Timestamp
import java.time.ZoneId
import java.util.*

@Service
class DBServiceImpl(
    val emvStopListRecordRepository: EmvStopListRecordRepository,
    val emvStopListRepository: EmvStopListRepository,
    val virtStopListRecordRepository: VirtStopListRecordRepository,
    val virtStopListRepository: VirtStopListRepository
) : DBService {

    override suspend fun getEmvStopLists(hashType: HashType, projectId: UUID): Either<Throwable, List<ru.sbertroika.stop.list.model.db.EmvStopListRecord>> = Either.catch {
        val sort = Sort.by(Sort.Direction.ASC, "uid")
        emvStopListRecordRepository.findAllByProjectIdAndHashType(projectId, hashType.ordinal, sort).toList()
    }

    override suspend fun getLastStopList(hashType: HashType, projectId: UUID): Either<Throwable, EmvStopList?> = Either.catch {
        emvStopListRepository.findLast(projectId, hashType.ordinal)
    }

    override suspend fun saveEmvStopListRecord(record: ru.sbertroika.stop.list.model.db.EmvStopListRecord): Either<Throwable, ru.sbertroika.stop.list.model.db.EmvStopListRecord> = Either.catch {
        emvStopListRecordRepository.save(record)
    }

    override suspend fun insertEmvStopListRecords(records: List<ru.sbertroika.tkp3.stoplist.processing.model.EmvStopListRecord>): Either<Throwable, List<ru.sbertroika.stop.list.model.db.EmvStopListRecord>> =
        Either.catch {
            val list = records.map { rec ->
                ru.sbertroika.stop.list.model.db.EmvStopListRecord(
                    projectId = rec.projectId,
                    carrierId = rec.carrierId,
                    createdAt = Timestamp.valueOf(rec.createdAt!!.toLocalDateTime()),
                    uid = rec.uid.toString(),
                    hash = rec.hash,
                    hashType = rec.hashType.ordinal,
                    version = rec.createVersion
                )
            }.toList()
            list.forEach { record ->
                val stopListRec = emvStopListRecordRepository.findByProjectIdAndHashTypeAndHash(record.projectId!!, record.hashType!!, record.hash!!)
                if (stopListRec == null) {
                    emvStopListRecordRepository.save(record)
                }
            }
            list
//            emvStopListRecordRepository.saveAll(
//                records.map { rec ->
//                    ru.sbertroika.stop.list.model.db.EmvStopListRecord(
//                        projectId = rec.projectId,
//                        carrierId = rec.carrierId,
//                        createdAt = Timestamp.valueOf(rec.createdAt!!.toLocalDateTime()),
//                        uid = rec.uid.toString(),
//                        hash = rec.hash,
//                        hashType = rec.hashType.ordinal,
//                        version = rec.createVersion
//                    )
//                }.toList()
//            ).toList()
        }

    override suspend fun resetByProject(projectId: UUID): Either<Throwable, Unit> = Either.catch {
        emvStopListRecordRepository.deleteAllByProjectId(projectId)
    }

    override suspend fun resetVirtByProject(projectId: UUID): Either<Throwable, Unit> = Either.catch {
        virtStopListRecordRepository.deleteAllByProjectId(projectId)
    }

    override suspend fun deleteAllByVersionAndHash(records: List<ru.sbertroika.tkp3.stoplist.processing.model.EmvStopListRecord>): Either<Throwable, List<ru.sbertroika.tkp3.stoplist.processing.model.EmvStopListRecord>> =
        Either.catch {
            records.mapNotNull { record ->
                // TODO think about batch
                val stopListRec = emvStopListRecordRepository.findByProjectIdAndHashTypeAndHash(record.projectId!!, record.hashType.ordinal, record.hash)
                stopListRec?.let {
                    emvStopListRecordRepository.delete(it)
                    record.copy(
                        projectId = it.projectId,
                        createdAt = it.createdAt!!.toInstant().atZone(ZoneId.of("UTC")),
                        createVersion = it.version,
                        carrierId = it.carrierId
                    )
                }
            }.fold(mutableListOf()) { a, c ->
                a.apply { add(c) }
            }
        }

    override suspend fun insertEmvStopList(sl: EmvStopList): Either<Throwable, EmvStopList> = Either.catch {
        emvStopListRepository.save(sl)
    }


    override suspend fun getVirtStopLists(projectId: UUID): Either<Throwable, List<VirtStopListRecord>> = Either.catch {
        val sort = Sort.by(Sort.Direction.ASC, "uid")
        virtStopListRecordRepository.findAllByProjectId(projectId, sort).toList()
    }

    override suspend fun saveVirtStopListRecord(record: VirtStopListRecord): Either<Throwable, VirtStopListRecord> = Either.catch {
        virtStopListRecordRepository.save(record)
    }

    override suspend fun insertVirtStopListRecords(records: List<ru.sbertroika.tkp3.stoplist.processing.model.VirtStopListRecord>): Either<Throwable, List<VirtStopListRecord>> =
        Either.catch {
            val list = records.map { rec ->
                VirtStopListRecord(
                    projectId = rec.projectId,
                    carrierId = rec.carrierId,
                    createdAt = Timestamp.valueOf(rec.createdAt!!.toLocalDateTime()),
                    uid = rec.uid,
                    version = rec.createVersion
                )
            }.toList()
            list.forEach { record ->
                val stopListRec = virtStopListRecordRepository.findByProjectIdAndUid(record.projectId!!, record.uid!!)
                if (stopListRec == null) {
                    virtStopListRecordRepository.save(record)
                }
            }
            list
        }

    override suspend fun deleteAllByVersion(records: List<ru.sbertroika.tkp3.stoplist.processing.model.VirtStopListRecord>): Either<Throwable, List<ru.sbertroika.tkp3.stoplist.processing.model.VirtStopListRecord>> =
        Either.catch {
            records.mapNotNull { record ->
                // TODO think about batch
                val stopListRec = virtStopListRecordRepository.findByProjectIdAndUid(record.projectId!!, record.uid)
                stopListRec?.let {
                    virtStopListRecordRepository.delete(it)
                    record.copy(
                        projectId = it.projectId,
                        createdAt = it.createdAt!!.toInstant().atZone(ZoneId.of("UTC")),
                        createVersion = it.version,
                        carrierId = it.carrierId
                    )
                }
            }.fold(mutableListOf()) { a, c ->
                a.apply { add(c) }
            }
        }
    override suspend fun insertVirtStopList(sl: VirtStopList): Either<Throwable, VirtStopList> = Either.catch {
        virtStopListRepository.save(sl)
    }

    override suspend fun resetVirtStopListByProject(projectId: UUID): Either<Throwable, Unit> = Either.catch {
        virtStopListRecordRepository.deleteAllByProjectId(projectId)
    }

    override suspend fun getLastStopList(projectId: UUID): Either<Throwable, VirtStopList?> = Either.catch {
        virtStopListRepository.findTopByProjectIdOrderByVersionDesc(projectId)
    }

}