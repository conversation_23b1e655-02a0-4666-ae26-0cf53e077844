package ru.sbertroika.tkp3.stoplist.processing.util

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.core.json.JsonReadFeature
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import java.nio.ByteBuffer
import java.nio.ByteOrder

fun mapper(): ObjectMapper = ObjectMapper()
    .configure(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS.mappedFeature(), true)
    .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
    .registerKotlinModule()
    .registerModule(Jdk8Module())
    .registerModule(JavaTimeModule())
    .configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)
    .setSerializationInclusion(JsonInclude.Include.NON_NULL)

fun ByteArray.toHexString() = asUByteArray().joinToString("") {
    it.toString(16).padStart(2, '0').uppercase()
}

fun String.hexToByteArray(): ByteArray = ByteArray(this.length / 2) {
    this.substring(it * 2, it * 2 + 2).toInt(16).toByte()
}

fun ByteArray.cardUidToDec(): Long {
    if (this.size == 4) {
        val buffer = ByteBuffer.allocate(ULong.SIZE_BYTES)
        val uid = this.clone()
        if (ByteOrder.nativeOrder() != ByteOrder.LITTLE_ENDIAN) {
            uid.reverse()
        }
        buffer.put(uid).order(ByteOrder.LITTLE_ENDIAN)
        return buffer.getLong(0)
    }
    val buffer = ByteBuffer.allocate(8)
    val uid = this.clone()
    if (ByteOrder.nativeOrder() == ByteOrder.LITTLE_ENDIAN) {
        uid.reverse()
    }
    buffer.put(uid).order(ByteOrder.LITTLE_ENDIAN)
    return buffer.getLong(0)
}
