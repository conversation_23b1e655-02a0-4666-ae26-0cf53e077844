package ru.sbertroika.tkp3.stoplist.processing.input

import arrow.core.Either
import arrow.core.right
import com.fasterxml.jackson.module.kotlin.readValue
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.kafka.support.Acknowledgment
import org.springframework.stereotype.Component
import ru.sbertroika.stoplist.StoplistTools
import ru.sbertroika.tkp3.stoplist.api.model.EmvStopList
import ru.sbertroika.tkp3.stoplist.api.model.EmvStopListOperationRecord
import ru.sbertroika.tkp3.stoplist.api.model.HashType
import ru.sbertroika.tkp3.stoplist.api.model.HashType.*
import ru.sbertroika.tkp3.stoplist.api.model.StopListOperationType
import ru.sbertroika.tkp3.stoplist.processing.model.EmvStopListRecord
import ru.sbertroika.tkp3.stoplist.processing.model.FileSizeZeroError
import ru.sbertroika.tkp3.stoplist.processing.output.DBService
import ru.sbertroika.tkp3.stoplist.processing.output.EMVEmissionService
import ru.sbertroika.tkp3.stoplist.processing.output.KafkaService
import ru.sbertroika.tkp3.stoplist.processing.output.StopListFileService
import java.sql.Timestamp
import java.time.Duration
import java.time.Instant
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.*

@Component
class EmvStopListConsumer(
    val dbService: DBService,
    val emvEmissionService: EMVEmissionService,
    val kafkaService: KafkaService,
    private val stopListFileService: StopListFileService,

    @Value("\${diff_enable}")
    private val diffEnable: Boolean = false
) {

    private val logger = LoggerFactory.getLogger(this::class.java)
    private val mapper = ru.sbertroika.tkp3.stoplist.processing.util.mapper()

    @KafkaListener(groupId = "\${spring.kafka.emv_stop_list_processing_in_group}", topics = ["\${spring.kafka.emv_stop_list_in_topic}"])
    fun receive(record: ConsumerRecord<String, String>, acknowledgment: Acknowledgment) = runBlocking {
        try {
            val stopList = mapper.readValue<EmvStopList>(record.value())

            if (stopList.records.isEmpty()) {
                logger.warn("Empty records")
                acknowledgment.acknowledge()
                return@runBlocking
            }

            if (stopList.isReset) {
                dbService.resetByProject(stopList.project).fold(
                    {
                        logger.error("Error reset stop-list by projectId ${stopList.project}")
                        acknowledgment.acknowledge()
                        return@runBlocking
                    }, {}
                )
            }

            // Извлекаем только хэши sha256, так как стоп-листы формируются по хэшам а на вход может прийти смешанный стоп-лист
            extract(stopList, SHA256).fold(
                {
                    acknowledgment.nack(Duration.ofMinutes(5))
                    return@runBlocking
                },
                { records ->
                    if (records.first.isNotEmpty() || records.second.isNotEmpty()) {
                        try {
                            if (dbService.insertEmvStopListRecords(records.first).isLeft()) {
                                logger.error("Error insert records")
                                acknowledgment.nack(Duration.ofMinutes(5))
                                return@runBlocking
                            }
                            val deleted = dbService.deleteAllByVersionAndHash(records.second).fold(
                                {
                                    logger.error("Error delete records")
                                    acknowledgment.nack(Duration.ofMinutes(5))
                                    return@runBlocking
                                },
                                { it }
                            )
                            if (kafkaService.sendEmvStopListRecords(records.first).isLeft() || kafkaService.sendEmvStopListRecords(deleted).isLeft()) {
                                logger.error("Error send journal")
                                acknowledgment.nack(Duration.ofMinutes(5))
                                return@runBlocking
                            }

                            makeStopList(stopList, SHA256, stopList.project).fold(
                                {
                                    acknowledgment.nack(Duration.ofMinutes(5))
                                },
                                { }
                            )
                        } catch (e: Exception) {
                            logger.error("StopList processing DB operation error: ", e)
                            acknowledgment.nack(Duration.ofMinutes(5))
                            return@runBlocking
                        }
                    }
                }
            )

            // Аналогично только для PAR
            extract(stopList, PAR).fold(
                {
                    acknowledgment.nack(Duration.ofMinutes(5))
                    return@runBlocking
                },
                { records ->
                    if (records.first.isNotEmpty() || records.second.isNotEmpty()) {
                        try {
                            if (dbService.insertEmvStopListRecords(records.first).isLeft()) {
                                logger.error("Error insert records")
                                acknowledgment.nack(Duration.ofMinutes(5))
                                return@runBlocking
                            }
                            val deleted = dbService.deleteAllByVersionAndHash(records.second).fold(
                                {
                                    logger.error("Error delete records")
                                    acknowledgment.nack(Duration.ofMinutes(5))
                                    return@runBlocking
                                },
                                { it }
                            )
                            if (kafkaService.sendEmvStopListRecords(records.first).isLeft() || kafkaService.sendEmvStopListRecords(deleted).isLeft()) {
                                logger.error("Error send journal")
                                acknowledgment.nack(Duration.ofMinutes(5))
                                return@runBlocking
                            }

                            makeStopList(stopList, PAR, stopList.project).fold(
                                {
                                    acknowledgment.nack(Duration.ofMinutes(5))
                                },
                                { }
                            )
                        } catch (e: Exception) {
                            logger.error("StopList processing DB operation error: ", e)
                            acknowledgment.nack(Duration.ofMinutes(5))
                            return@runBlocking
                        }
                    }
                }
            )

            // Аналогично только для HMAC_SHA1
            extract(stopList, HMAC_SHA1).fold(
                {
                    acknowledgment.nack(Duration.ofMinutes(5))
                    return@runBlocking
                },
                { records ->
                    if (records.first.isNotEmpty() || records.second.isNotEmpty()) {
                        try {
                            if (dbService.insertEmvStopListRecords(records.first).isLeft()) {
                                logger.error("Error insert records")
                                acknowledgment.nack(Duration.ofMinutes(5))
                                return@runBlocking
                            }
                            val deleted = dbService.deleteAllByVersionAndHash(records.second).fold(
                                {
                                    logger.error("Error delete records")
                                    acknowledgment.nack(Duration.ofMinutes(5))
                                    return@runBlocking
                                },
                                { it }
                            )
                            if (kafkaService.sendEmvStopListRecords(records.first).isLeft() || kafkaService.sendEmvStopListRecords(deleted).isLeft()) {
                                logger.error("Error send journal")
                                acknowledgment.nack(Duration.ofMinutes(5))
                                return@runBlocking
                            }

                            makeStopList(stopList, HMAC_SHA1, stopList.project).fold(
                                {
                                    acknowledgment.nack(Duration.ofMinutes(5))
                                },
                                { }
                            )
                        } catch (e: Exception) {
                            logger.error("StopList processing DB operation error: ", e)
                            acknowledgment.nack(Duration.ofMinutes(5))
                            return@runBlocking
                        }
                    }
                }
            )

            // Аналогично только для BIN
            extract(stopList, BIN).fold(
                {
                    acknowledgment.nack(Duration.ofMinutes(5))
                    return@runBlocking
                },
                { records ->
                    if (records.first.isNotEmpty() || records.second.isNotEmpty()) {
                        try {
                            if (dbService.insertEmvStopListRecords(records.first).isLeft()) {
                                logger.error("Error insert records")
                                acknowledgment.nack(Duration.ofMinutes(5))
                                return@runBlocking
                            }
                            val deleted = dbService.deleteAllByVersionAndHash(records.second).fold(
                                {
                                    logger.error("Error delete records")
                                    acknowledgment.nack(Duration.ofMinutes(5))
                                    return@runBlocking
                                },
                                { it }
                            )
                            if (kafkaService.sendEmvStopListRecords(records.first).isLeft() || kafkaService.sendEmvStopListRecords(deleted).isLeft()) {
                                logger.error("Error send journal")
                                acknowledgment.nack(Duration.ofMinutes(5))
                                return@runBlocking
                            }

                            makeStopList(stopList, BIN, stopList.project).fold(
                                {
                                    acknowledgment.nack(Duration.ofMinutes(5))
                                },
                                { }
                            )
                        } catch (e: Exception) {
                            logger.error("StopList processing DB operation error: ", e)
                            acknowledgment.nack(Duration.ofMinutes(5))
                            return@runBlocking
                        }
                    }
                }
            )

            acknowledgment.acknowledge()
        } catch (e: Exception) {
            logger.error("error process transaction ${record.key()}", e)
            acknowledgment.acknowledge()
        }
    }

    private suspend fun makeStopList(stopList: EmvStopList, hashType: HashType, projectId: UUID): Either<Throwable, Unit> = Either.catch {
        dbService.getEmvStopLists(hashType, projectId).fold(
            {
                logger.error("Error fetch stop list", it)
                throw Error(it)
            },
            { list ->
                val version = ZonedDateTime.now(ZoneId.of("UTC")).format(fmtVer).toLong()
                val full = stopListFileService.makeEmvStopListFileFull(hashType, list, version).fold(
                    {
                        logger.error("Error create stop list file", it)
                        throw Error(it)
                    },
                    { it }
                )

                val last = dbService.getLastStopList(hashType, stopList.project).fold(
                    {
                        logger.error("Error fetch last stop list", it)
                        throw Error(it)
                    },
                    { it }
                )
                val diff = if (last != null && diffEnable) {
                    stopListFileService.makeEmvStopListFileDiff(hashType, full.fileName, last.fullFileName!!, version).fold(
                        {
                            if (it is FileSizeZeroError) {
                                null
                            } else {
                                logger.error("Error make diff file", it)
                                throw Error(it)
                            }
                        },
                        { it }
                    )
                } else null

                dbService.insertEmvStopList(
                    ru.sbertroika.stop.list.model.db.EmvStopList(
                        projectId = stopList.project,
                        createdAt = Timestamp.from(Instant.now()),
                        hashType = hashType.ordinal,
                        version = version,
                        fullFileName = full.fileName,
                        fullCrc32 = full.crc32,
                        diffFileName = diff?.fileName,
                        diffCrc32 = diff?.crc32
                    )
                )
            }
        )
    }

    private suspend fun extract(stopList: EmvStopList, hashType: HashType): Either<Throwable, Pair<MutableList<EmvStopListRecord>, MutableList<EmvStopListRecord>>> = Either.catch {
        val appendRecordsList = mutableListOf<EmvStopListRecord>()
        val deleteRecordsList = mutableListOf<EmvStopListRecord>()

        stopList.records.filter { it.hashType == hashType }.forEach { stopListRecord ->
            if (stopListRecord.type == StopListOperationType.APPEND) {
                emvEmissionService.emissionBySha256(stopListRecord.hash).fold(
                    { e ->
                        logger.error("emission failed ${stopListRecord.hash}", e)
                        throw Error("Error emission")
                    },
                    { carrierId ->
                        appendRecordsList += toAppendRecord(stopListRecord, carrierId, stopList.version, stopList.project)
                    }
                )
            } else if (stopListRecord.type == StopListOperationType.DELETE) {
                deleteRecordsList += toDeleteRecord(stopListRecord, stopList.version, stopList.project)
            }
        }

        return Pair(appendRecordsList, deleteRecordsList).right()
    }

    private fun toAppendRecord(rec: EmvStopListOperationRecord, carrierId: String, version: Long, projectId: UUID) = EmvStopListRecord(
        projectId = projectId,
        recordAt = ZonedDateTime.now(ZoneId.of("UTC")),
        carrierId = UUID.fromString(carrierId),
        createdAt = ZonedDateTime.now(ZoneId.of("UTC")),
        deletedAt = null,
        uid = calcUid(rec.hash),
        hash = rec.hash,
        hashType = rec.hashType,
        createVersion = version,
        deleteVersion = null,
        type = StopListOperationType.APPEND
    )

    private fun toDeleteRecord(rec: EmvStopListOperationRecord, version: Long, projectId: UUID) = EmvStopListRecord(
        projectId = projectId,
        recordAt = ZonedDateTime.now(ZoneId.of("UTC")),
        carrierId = null, // null поля заполняются при удалении
        createdAt = null,
        uid = calcUid(rec.hash),
        hash = rec.hash,
        hashType = rec.hashType,
        createVersion = null,
        deleteVersion = version,
        deletedAt = ZonedDateTime.now(ZoneId.of("UTC")),
        type = StopListOperationType.DELETE
    )

    private fun calcUid(hash: String): ULong = StoplistTools.getUniqueIndex(hash)!!.toULong()

    companion object {
        private val fmtVer = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS")
    }
}