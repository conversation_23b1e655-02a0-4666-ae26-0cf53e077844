package ru.sbertroika.tkp3.stoplist.processing.output

import arrow.core.Either
import arrow.core.left
import io.grpc.ManagedChannelBuilder
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Service
import ru.sbertroika.emv.emission.v1.CardHash
import ru.sbertroika.emv.emission.v1.EMVEmissionServiceGrpcKt
import ru.sbertroika.emv.emission.v1.EmissionRequest
import ru.sbertroika.emv.emission.v1.HashType


@Service
@ConditionalOnProperty(name = ["develop.emission_stub"], havingValue = "false", matchIfMissing = true)
class EMVEmissionServiceImpl(
    @Value("\${emv_emission_url}")
    private val emvEmissionServiceUrl: String
) : EMVEmissionService {

    private final val channel = ManagedChannelBuilder.forTarget(emvEmissionServiceUrl)
        .usePlaintext()
        .enableRetry()
        .maxRetryAttempts(3)
        .build()
    val client = EMVEmissionServiceGrpcKt.EMVEmissionServiceCoroutineStub(channel)

    override suspend fun emissionBySha256(hash: String): Either<Error, String> = emission(hash, HashType.SHA256)

    override suspend fun emissionByPar(hash: String): Either<Error, String> = emission(hash, HashType.SHA256)

    private suspend fun emission(hash: String, type: HashType): Either<Error, String> {
        try {
            val request = EmissionRequest.newBuilder()
                .setHash(
                    CardHash.newBuilder()
                        .setHash(hash)
                        .setHashType(type)
                )
                .build()
            val response = client.emission(request)
            if (response.hasError())
                return Error(response.error.message).left()
            return Either.Right(response.cardId)
        } catch (e: Exception) {
            return Either.Left(Error(e))
        }
    }
}