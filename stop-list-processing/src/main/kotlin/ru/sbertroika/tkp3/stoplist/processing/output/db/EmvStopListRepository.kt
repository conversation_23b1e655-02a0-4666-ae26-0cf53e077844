package ru.sbertroika.tkp3.stoplist.processing.output.db

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import ru.sbertroika.stop.list.model.db.EmvStopList
import java.util.*

interface EmvStopListRepository : CoroutineCrudRepository<EmvStopList, UUID> {

    @Query("SELECT * from emv_stop_list_view where project_id = :projectId and hash_type = :hashType")
    suspend fun findLast(projectId: UUID, hashType: Int): EmvStopList?
}