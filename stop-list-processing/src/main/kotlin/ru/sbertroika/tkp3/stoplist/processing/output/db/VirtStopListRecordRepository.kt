package ru.sbertroika.tkp3.stoplist.processing.output.db

import org.springframework.data.domain.Sort
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import ru.sbertroika.stop.list.model.db.VirtStopListRecord
import java.util.*

interface VirtStopListRecordRepository: CoroutineCrudRepository<VirtStopListRecord, UUID> {

    suspend fun findByProjectIdAndUid(projectId: UUID, uid: String): VirtStopListRecord?

    suspend fun findAllByProjectId(projectId: UUID, sort: Sort): List<VirtStopListRecord>

    suspend fun deleteAllByProjectId(projectId: UUID)
}