import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
import org.gradle.api.tasks.testing.Test

plugins {
    idea
    `java-library`
    kotlin("jvm")
    `maven-publish`
    kotlin("plugin.spring") version libs.versions.kotlin.get() apply false
    id("com.google.protobuf") version libs.versions.protobufPlugin.get()
    id("org.springframework.boot") version "3.0.1"
    id("io.spring.dependency-management") version "1.1.0"
}

group = "ru.sbertroika.stop-list"
version = rootProject.version

configurations {
    compileOnly {
        extendsFrom(configurations.annotationProcessor.get())
    }
}

repositories {
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-public/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-releases/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-snapshots/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
}

dependencies {
    implementation(project(":common-stop-list"))
    implementation(project(":stop-list-api"))
    implementation(project(":stop-list-private-api"))
    implementation(project(":stop-list-model"))
    implementation(project(":lib-stop-list"))
    implementation("ru.sbertroika.common:common-api:1.0.0")
    implementation("ru.sbertroika.common:grpc-starter:1.0.0")
    implementation("ru.sbertroika.qr:qr-emission-api:1.0.2")
    implementation("ru.sbertroika.emv:emv-emission-api:1.0.0")

    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.kafka:spring-kafka:3.0.10")
    implementation("org.springframework.boot:spring-boot-starter-webflux")
    implementation("org.springframework.boot:spring-boot-starter-data-r2dbc")

    //Kotlin
    implementation(libs.kotlin.stdlib)
    implementation(libs.kotlin.reflect)
    implementation("io.projectreactor.kotlin:reactor-kotlin-extensions")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-reactor")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core")
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.15.0")

    //Kotlin-ext
    implementation("io.arrow-kt:arrow-core:1.0.1")

    //gRPC
    implementation(libs.grpcKotlinStub)
    implementation(libs.grpcProtobuf)
    implementation(libs.protobufKotlin)
    implementation(libs.grpcStub)
    implementation("io.grpc:grpc-netty:1.52.1")
    implementation("io.grpc:grpc-okhttp:1.52.1")

    // Postgres
    runtimeOnly("org.postgresql:r2dbc-postgresql")
    runtimeOnly("io.r2dbc:r2dbc-pool")
    runtimeOnly("org.postgresql:postgresql:42.7.7")

    implementation("software.amazon.awssdk:s3:2.22.0")

    //Test
    testImplementation("org.junit.jupiter:junit-jupiter-api")
    testRuntimeOnly("org.junit.jupiter:junit-jupiter-engine")
    testCompileOnly("org.projectlombok:lombok")
    testImplementation("org.springframework.boot:spring-boot-starter-test") {
        // Exclude the test engine you don't need
        exclude(group = "org.junit.vintage", module = "junit-vintage-engine")
    }
}

kotlin {
    jvmToolchain(17)
}

tasks.withType<KotlinCompile>().configureEach {
    compilerOptions {
        freeCompilerArgs = listOf("-Xjsr305=strict")
    }
}

tasks.withType<Test>().configureEach {
    val skipProvider = providers.gradleProperty("skipTest")
    if (!skipProvider.isPresent()) {
        useJUnitPlatform()
        testLogging {
            showStandardStreams = true
        }
    }
}

tasks.named<Jar>("jar") {
    enabled = false
}
