#!/bin/bash

# Скрипт для локального тестирования GitLab CI/CD pipeline в Docker
# Имитирует выполнение задач в Docker контейнере

set -e

# Цвета для вывода
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Функции для вывода
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Проверка наличия Docker
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker не установлен. Установите Docker и попробуйте снова."
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker не запущен или нет прав доступа. Запустите Docker и попробуйте снова."
        exit 1
    fi
    
    log_success "Docker доступен"
}

# Проверка наличия gradle.properties
check_gradle_props() {
    if [ -f "gradle.properties" ]; then
        log_info "Найден gradle.properties в корне проекта"
        return 0
    elif [ -f "$HOME/.gradle/gradle.properties" ]; then
        log_info "Найден gradle.properties в ~/.gradle/"
        return 0
    else
        log_warning "gradle.properties не найден. Создайте файл с учетными данными Nexus:"
        echo "mavenUser=your_username"
        echo "mavenPassword=your_password"
        return 1
    fi
}

# Функция для выполнения команды в Docker
run_in_docker() {
    local command="$1"
    local description="$2"
    local use_local_props="$3"
    
    log_info "Выполнение: $description"
    
    local docker_cmd="docker run --rm -it"
    
    # Монтируем текущую директорию
    docker_cmd="$docker_cmd -v $(pwd):/workspace"
    
    # Если нужно использовать локальные настройки
    if [ "$use_local_props" = "true" ] && [ -f "$HOME/.gradle/gradle.properties" ]; then
        docker_cmd="$docker_cmd -v $HOME/.gradle:/root/.gradle"
        log_info "Используются локальные настройки из ~/.gradle/"
    fi
    
    docker_cmd="$docker_cmd -w /workspace"
    docker_cmd="$docker_cmd openjdk:17-jdk-slim"
    docker_cmd="$docker_cmd bash -c \"apt-get update && apt-get install -y curl unzip && chmod +x ./gradlew && echo 'mavenUser=test' > gradle.properties && echo 'mavenPassword=test' >> gradle.properties && echo 'version=test-SNAPSHOT' >> gradle.properties && $command\""
    
    log_info "Команда Docker: $docker_cmd"
    
    if eval $docker_cmd; then
        log_success "$description завершено успешно"
    else
        log_error "$description завершилось с ошибкой"
        return 1
    fi
}

# Основные команды
build() {
    run_in_docker "./gradlew build --no-daemon" "Сборка проекта" "$1"
}

test() {
    run_in_docker "./gradlew test --no-daemon" "Запуск тестов" "$1"
}

publish_snapshot() {
    run_in_docker "./gradlew publishToNexusSnapshot --no-daemon" "Публикация SNAPSHOT" "$1"
}

publish_release() {
    run_in_docker "./gradlew publishToNexus --no-daemon" "Публикация релиза" "$1"
}

release() {
    run_in_docker "apt-get install -y git && ./gradlew release --no-daemon" "Выпуск релиза" "$1"
}

# Функция помощи
show_help() {
    echo "Использование: $0 [КОМАНДА] [ОПЦИИ]"
    echo ""
    echo "Команды:"
    echo "  build              Сборка проекта"
    echo "  test               Запуск тестов"
    echo "  publish-snapshot   Публикация SNAPSHOT версии"
    echo "  publish-release    Публикация релизной версии"
    echo "  release            Выпуск релиза с тегом"
    echo "  all                Полный цикл (build + test + publish-snapshot)"
    echo "  help               Показать эту справку"
    echo ""
    echo "Опции:"
    echo "  --local-props      Использовать локальные настройки из ~/.gradle/"
    echo ""
    echo "Примеры:"
    echo "  $0 build"
    echo "  $0 test --local-props"
    echo "  $0 all"
}

# Основная логика
main() {
    local command=""
    local use_local_props="false"
    
    # Парсинг аргументов
    while [[ $# -gt 0 ]]; do
        case $1 in
            --local-props)
                use_local_props="true"
                shift
                ;;
            build|test|publish-snapshot|publish-release|release|all|help)
                command="$1"
                shift
                ;;
            *)
                log_error "Неизвестный аргумент: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # Проверки
    check_docker
    check_gradle_props
    
    # Выполнение команды
    case $command in
        build)
            build "$use_local_props"
            ;;
        test)
            test "$use_local_props"
            ;;
        publish-snapshot)
            publish_snapshot "$use_local_props"
            ;;
        publish-release)
            publish_release "$use_local_props"
            ;;
        release)
            release "$use_local_props"
            ;;
        all)
            log_info "Запуск полного цикла..."
            build "$use_local_props"
            test "$use_local_props"
            publish_snapshot "$use_local_props"
            log_success "Полный цикл завершен"
            ;;
        help|"")
            show_help
            ;;
        *)
            log_error "Неизвестная команда: $command"
            show_help
            exit 1
            ;;
    esac
}

# Запуск основной функции
main "$@" 