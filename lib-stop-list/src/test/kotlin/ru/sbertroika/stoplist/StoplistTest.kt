package ru.sbertroika.stoplist

import org.junit.jupiter.api.Test
import java.io.File

class StoplistTest {

    @Test
    fun test() {
        val outF = File("test.diff").absolutePath
        val firstF = File("src/test/resources/pan_hash_sha_256_stoplist_500k.bin").absolutePath
        val secondF = File("src/test/resources/pan_hash_sha_256_stoplist_1M.bin").absolutePath
        println(firstF)
        println(secondF)
        println(outF)
        val startTime = System.currentTimeMillis()
        val res = Stoplist().generateDiff(
            pathToOldFile = firstF,
            pathToNewFile = secondF,
            pathToDiffFile = outF,
            elementSize = StoplistElementSize.SL_PAN_SHA256
        )
        println("time: ${System.currentTimeMillis() - startTime} ms")
        println(res)
    }

    @Test
    fun testGen() {
//        val pans = listOf<String>(
//            "2202241891810949",
//            "2202246397529570",
//            "4893477799388642",
//            "4893474382426939",
//            "5368295117895004",
//        )
//        val map = mutableMapOf<ULong, String>()
//        for (pan in pans) {
//            val sha256 = pan.sha256()
//        }
////        val sha256 = "a19d76b237ec8af99b9cb5fc7055883628082508a83f678e07cd2549acc8db9d"
//
//        val gen = StoplistTools.getUniqueIndex(sha256)!!.toULong()
//        println(gen)

    }
}