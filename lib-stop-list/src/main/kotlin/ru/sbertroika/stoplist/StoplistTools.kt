package ru.sbertroika.stoplist

object StoplistTools {
    init {
        System.loadLibrary("stoplist-jni")
    }

    /**
     * Формирует уникальный индекс от хешированного PAN
     *
     * @param panHash
     * хеш от PAN карты
     *
     * @return уникальный индекс
     *         или null в случае некорректного panHash
     */
    fun getUniqueIndex(panHash: String): Long? {
        if (panHash.isBlank()) {
            return null
        }

        return nativeGetUniqueIndex(panHash)
    }

    // Нативные методы
    private external fun nativeGetUniqueIndex(panHash: String): Long
}
