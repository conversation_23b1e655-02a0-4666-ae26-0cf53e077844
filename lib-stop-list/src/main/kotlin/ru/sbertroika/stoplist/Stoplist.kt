package ru.sbertroika.stoplist

import java.io.File

class Stoplist {

    init {
        System.loadLibrary("stoplist-jni")
    }

    /**
     * Генерация diff файла
     *
     * @param pathToOldFile
     * путь к файлу с исходными данными
     *
     * @param pathToNewFile
     * путь к файлу с новыми данными
     *
     * @param pathToDiffFile
     * путь к файлу, куда будет сохранён результат выполнения
     *
     * @param elementSize
     * размер одного элемента данных стоп-листа
     * @see StoplistElementSize
     *
     * @return [StoplistResult.SUCCESS] - в случае успеха, иначе код ошибки
     * @see StoplistResult
     */
    fun generateDiff(pathToOldFile: String,
                     pathToNewFile: String,
                     pathToDiffFile: String,
                     elementSize: StoplistElementSize): StoplistResult  {

        if (pathToOldFile.isBlank() || pathToNewFile.isBlank() || pathToDiffFile.isBlank()) {
            return StoplistResult.E_FILENAME_BLANK
        }

        if (!File(pathToOldFile).exists() || !File(pathToNewFile).exists()) {
            return StoplistResult.E_FILENAME_NO_EXIST
        }

        nativeGenerateDiff(pathToOldFile, pathToNewFile, pathToDiffFile, elementSize.value)

        return StoplistResult.SUCCESS
    }

    /**
     * Инициализация библиотеки
     *
     * @param pathToFile
     * путь к файлу для сохранения логов
     *
     * @param hashKey
     * ключ шифрования
     * Если ключ не задан, то проверка по стоп-листам, где он используется, не выполняется
     *
     * @param isDebug
     * включение/отключение отладочного логирования библиотеки
     *
     * @param sizeBackupLog
     * максимальный размер файла логов в МБ. Если не задан, то равен 4 МБ
     *
     * @return true - в случаи успешной инициализации
     */
    external fun initLib(pathToFile: String,
                         hashKey: String = "",
                         isDebug: Boolean = false,
                         sizeBackupLog: Short = 0): Boolean

    /**
     * Находится ли UID карты в стоп-листе
     *
     * @param pathToFile
     * путь к файлу стоп-листа
     *
     * @param UID
     * UID карты
     *
     * @return результат поиска или код ошибки
     * @see StoplistStatus
     */
    external fun isStopUid(pathToFile: String, UID: String): StoplistStatus

    /**
     * Находится ли PAN карты в стоп-листе с шифрованием SHA-256
     *
     * @param pathToFile
     * путь к файлу стоп-листа
     * @param PAN
     * PAN карты
     *
     * @return результат поиска или код ошибки
     * @see StoplistStatus
     */
    external fun isStopPanSha256(pathToFile: String, PAN: String): StoplistStatus

    /**
     * Находится ли PAN карты в стоп-листе с шифрованием HMAC-SHA256
     *
     * @param pathToFile
     * путь к файлу стоп-листа
     *
     * @param PAN
     * PAN карты
     *
     * @return результат поиска или код ошибки
     * @see StoplistStatus
     */
    external fun isStopPanHmacSha256(pathToFile: String, PAN: String): StoplistStatus

    /**
     * Находится ли PAN карты в стоп-листе с шифрованием HMAC-SHA1
     *
     * @param pathToFile
     * путь к файлу стоп-листа
     *
     * @param PAN
     * PAN карты
     *
     * @return результат поиска или код ошибки
     * @see StoplistStatus
     */
    external fun isStopPanHmacSha1(pathToFile: String, PAN: String): StoplistStatus

    /**
     * Находится ли PAN карты в стоп-листе по PAR
     *
     * @param pathToFile
     * путь к файлу стоп-листа
     *
     * @param PAR
     * PAR карты
     *
     * @return результат поиска или код ошибки
     * @see StoplistStatus
     */
    external fun isStopPar(pathToFile: String, PAR: String): StoplistStatus

    /**
     * Находится ли PAN карты в стоп-листе по BIN EMV
     *
     * @param pathToFile
     * путь к файлу стоп-листа
     *
     * @param PAN
     * PAN карты
     *
     * @return результат поиска или код ошибки
     * @see StoplistStatus
     */
    external fun isStopBin(pathToFile: String, PAN: String): StoplistStatus

    /**
     * Применить diff-файл стоп-листов
     *
     * @param pathToSrcFile
     * путь к файлу, к котрому надо применить diff
     * @param pathToDiffFile
     * путь к diff-файлу
     *
     * @return true - если diff-файл применился без ошибок, иначе - false
     */
    external fun acceptDiff(pathToSrcFile: String, pathToDiffFile: String): Boolean

    external fun testIsStopUid()
    external fun testIsStopPanGOST()

    external fun testIsStopPanSha256()

    external fun testIsStopPanHmacSha256()
    external fun testIsStopPanHmacSha1()
    external fun testIsStopPanHmac()
    external fun testIsStopPar()
    external fun testIsStopBin()

    /**
     * Нативная реализация генерации diff файла
     */
    private external fun nativeGenerateDiff(pathToOldFile: String,
                                            pathToNewFile: String,
                                            pathToDiffFile: String,
                                            elementSize: Int)
}
