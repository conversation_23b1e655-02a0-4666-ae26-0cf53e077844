package ru.sbertroika.stoplist

enum class StoplistStatus(var code: Int) {
    /**
     * Возникла ошибка при выполнении поиска
     */
    ERROR(-1),

    /**
     * Идентификатор (PAN, PAR, BIN или UID) не найден в стоп-листе
     */
    NOT_FOUND(0),

    /**
     * Идентификатор (PAN, PAR, BIN или UID) найден в стоп-листе
     */
    FOUND(1);

    companion object {
        fun fromInt(code: Int): StoplistStatus? {
            val map = values().associateBy(StoplistStatus::code)

            return map[code]
        }
    }
}
