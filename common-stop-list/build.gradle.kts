import com.google.protobuf.gradle.*
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    idea
    `java-library`
    kotlin("jvm")
    id("com.google.protobuf") version libs.versions.protobufPlugin.get()
    `maven-publish`
}

group = "ru.sbertroika.stop-list"
version = rootProject.version

repositories {
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-public/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-releases/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
    maven {
        url = uri("https://nexus.sbertroika.tech/repository/maven-snapshots/")
        credentials {
            username = providers.gradleProperty("mavenUser").get()
            password = providers.gradleProperty("mavenPassword").get()
        }
    }
}

dependencies {
    implementation(libs.grpcProtobuf)
    implementation(libs.grpcStub)
    implementation(libs.grpcKotlinStub)
    implementation(libs.protobufJava)
    implementation(libs.protobufKotlin)

    //Kotlin
    implementation(libs.kotlin.reflect)
    implementation(libs.kotlin.stdlib)
}

kotlin {
    jvmToolchain(17)
}

tasks.withType<KotlinCompile>().configureEach {
    compilerOptions {
        freeCompilerArgs = listOf("-Xjsr305=strict")
    }
}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:${libs.versions.protoC.get()}"
    }

    plugins {
        id("grpc") {
            artifact = "io.grpc:protoc-gen-grpc-java:${libs.versions.grpc.get()}"
        }
        id("grpckt") {
            artifact = "io.grpc:protoc-gen-grpc-kotlin:${libs.versions.grpcKotlin.get()}:jdk8@jar"
        }
    }

    generateProtoTasks {
        all().forEach { task ->
            task.builtins {
                id("kotlin")
            }
            task.plugins {
                id("grpc") {
                    option("lite")
                }
                id("grpckt") {
                    option("lite")
                }
            }
        }
    }

    generatedFilesBaseDir = "$projectDir/src/generated"
}

tasks.register<Delete>("cleanGeneratedProto") {
    delete(file("$projectDir/src/generated"))
}

// Настройка публикации в Nexus
publishing {
    publications {
        create<MavenPublication>("maven") {
            from(components["java"])
            
            pom {
                name.set("Common Stop List")
                description.set("Common library for stop list functionality")
                url.set("https://github.com/your-org/stop-list-domain")
                
                licenses {
                    license {
                        name.set("The Apache License, Version 2.0")
                        url.set("http://www.apache.org/licenses/LICENSE-2.0.txt")
                    }
                }
                
                developers {
                    developer {
                        id.set("sbertroika")
                        name.set("SBertroika Team")
                        email.set("<EMAIL>")
                    }
                }
            }
        }
    }
    
    repositories {
        maven {
            name = "nexusReleases"
            url = uri("https://nexus.sbertroika.tech/repository/maven-releases/")
            credentials {
                username = providers.gradleProperty("mavenUser").get()
                password = providers.gradleProperty("mavenPassword").get()
            }
        }
        maven {
            name = "nexusSnapshots"
            url = uri("https://nexus.sbertroika.tech/repository/maven-snapshots/")
            credentials {
                username = providers.gradleProperty("mavenUser").get()
                password = providers.gradleProperty("mavenPassword").get()
            }
        }
    }
}

// Задача для публикации в Nexus (выбирает правильный репозиторий в зависимости от версии)
tasks.register("publishToNexus") {
    dependsOn("publishMavenPublicationToNexusReleasesRepository")
    onlyIf {
        !version.toString().endsWith("-SNAPSHOT")
    }
    
    doLast {
        println("Published ${project.name} version ${version} to Nexus Releases")
    }
    group = "publishing"
    description = "Публикует релизную версию в Nexus"
}

tasks.register("publishToNexusSnapshot") {
    dependsOn("publishMavenPublicationToNexusSnapshotsRepository")
    onlyIf {
        version.toString().endsWith("-SNAPSHOT")
    }
    
    doLast {
        println("Published ${project.name} version ${version} to Nexus Snapshots")
    }
    group = "publishing"
    description = "Публикует SNAPSHOT версию в Nexus"
}
