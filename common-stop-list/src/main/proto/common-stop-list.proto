syntax = "proto3";

package ru.sbertroika.common.stop.list;

option java_multiple_files = true;
option java_package = "ru.sbertroika.common.stop.list";

enum StopListUpdateType {
  SLU_FULL = 0;
  SLU_DIFF = 1;
}

enum StopListType {
  SL_EMV = 0;
  SL_TROIKA = 1;
  SL_PROSTOR = 2;
}

enum StopListSubType {
  SLH_BIN = 0;
  SLH_SHA256 = 1;
  SLH_PAR = 2;
  SLH_HMAC_SHA1 = 3;
  SLH_HMAC_SHA256 = 4;
  SLH_HMAC_SHA256_SHA256 = 5;
  SLH_STRIBOG512 = 6;
}

message StopList {
  StopListUpdateType updateType = 1;        // Тип файла
  optional StopListSubType subType = 2;     // Подтип, может не быть
  string url = 3;                           // Ссылка на скачивание стоп-листа
  string crc = 4;                           // CRC32 для контроля целостности файла стоп-листа
}

message StopListUpdate {
  uint64 version = 1;                       // Новая версия стоп-листа
  StopListType type = 2;                    // Тип стоп-листа
  repeated StopList list = 3;               // Список обновлений
}
