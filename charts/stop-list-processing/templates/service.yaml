apiVersion: v1
kind: Service
metadata:
  name: {{ include "stop-list-processing.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "stop-list-processing.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.http.type }}
  ports:
    - name: http
      port: {{ .Values.service.http.port }}
      targetPort: {{ .Values.service.http.targetPort }}
      protocol: TCP
  selector:
    {{- include "stop-list-processing.selectorLabels" . | nindent 4 }}