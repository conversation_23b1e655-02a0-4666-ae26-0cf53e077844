apiVersion: v1
kind: Service
metadata:
  name: {{ include "stop-list-gate.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "stop-list-gate.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.grpc.type }}
  ports:
    - name: http
      port: {{ .Values.service.http.port }}
      targetPort: {{ .Values.service.http.targetPort }}
      protocol: TCP
    - name: grpc
      port: {{ .Values.service.grpc.port }}
      targetPort: {{ .Values.service.grpc.targetPort }}
      protocol: TCP
  selector:
    {{- include "stop-list-gate.selectorLabels" . | nindent 4 }}