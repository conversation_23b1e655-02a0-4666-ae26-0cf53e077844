#!/bin/bash
# Helm validation for all charts in charts/ folder

set -e

# Get namespace from user
read -p "Enter namespace for validation: " NAMESPACE

# Default stage to dev
STAGE="dev"

echo "Starting Helm validation for all charts in namespace: $NAMESPACE (stage: $STAGE)"
echo "============================================================================"

# Change to charts directory
cd "$(dirname "$0")"

# Initialize success counter
SUCCESS_COUNT=0
TOTAL_COUNT=0

# Iterate through all directories in charts/
for CHART_DIR in ./*/ ; do
  # Skip if not a directory or if it's the current directory
  if [[ ! -d "$CHART_DIR" || "$CHART_DIR" == "./" ]]; then
    continue
  fi
  
  CHART_NAME=$(basename "$CHART_DIR")
  
  # Skip hidden directories
  if [[ "$CHART_NAME" == .* ]]; then
    continue
  fi
  
  TOTAL_COUNT=$((TOTAL_COUNT + 1))
  
  echo ""
  echo "Validating chart: $CHART_NAME"
  echo "----------------------------------------"
  
  # Check if required files exist
  if [[ -f "$CHART_DIR/values.yaml" && -f "$CHART_DIR/values.$STAGE.yaml" ]]; then
    echo "✓ Found values.yaml and values.$STAGE.yaml"
    
    # Run helm validation
    echo "Running helm install --dry-run for $CHART_NAME..."
    
    if helm install --dry-run -n "$NAMESPACE" "$CHART_NAME" "$CHART_DIR" \
      -f "$CHART_DIR/values.yaml" \
      -f "$CHART_DIR/values.$STAGE.yaml" > /dev/null 2>&1; then
      echo "✓ Helm validation PASSED for $CHART_NAME"
      SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
    else
      echo "✗ Helm validation FAILED for $CHART_NAME"
      echo "Running with verbose output to show errors:"
      helm install --dry-run -n "$NAMESPACE" "$CHART_NAME" "$CHART_DIR" \
        -f "$CHART_DIR/values.yaml" \
        -f "$CHART_DIR/values.$STAGE.yaml"
      echo ""
      echo "Continuing with other charts..."
    fi
  else
    echo "✗ Skipping $CHART_NAME: missing values.yaml or values.$STAGE.yaml"
    if [[ ! -f "$CHART_DIR/values.yaml" ]]; then
      echo "  Missing: $CHART_DIR/values.yaml"
    fi
    if [[ ! -f "$CHART_DIR/values.$STAGE.yaml" ]]; then
      echo "  Missing: $CHART_DIR/values.$STAGE.yaml"
    fi
  fi
done

echo ""
echo "============================================================================"
echo "Helm validation completed!"
echo "Results: $SUCCESS_COUNT/$TOTAL_COUNT charts passed validation"

if [[ $SUCCESS_COUNT -eq $TOTAL_COUNT ]]; then
  echo "🎉 All charts validated successfully!"
  exit 0
else
  echo "⚠️  Some charts failed validation. Please check the output above."
  exit 1
fi
