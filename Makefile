docker-build-and-run: docker-build-gate docker-build-processing
	docker-compose up -d

docker-build: docker-build-gate docker-build-processing

start:
	docker-compose up -d

restart:
	docker-compose stop
	docker-compose rm -vf stop_list_gate
	docker-compose rm -vf stop_list_processing
	docker-compose up -d

restart-all:
	docker-compose stop && docker-compose rm -vf && docker-compose up -d

docker-rm:
	docker-compose stop && docker-compose rm -vf

docker-rm-all:
	docker-compose rm -vf

stop:
	docker-compose stop

rebuild-and-restart: stop docker-rm docker-build start

rebuild-and-restart-all: stop docker-rm-all docker-build start

docker-build-gate:
	@echo "[INFO] Сборка docker-образа stop-list-gate с пробросом gradle.properties"
	@mkdir -p .ci-gradle
	@if [ -f "$$HOME/.gradle/gradle.properties" ]; then \
		cp "$$HOME/.gradle/gradle.properties" .ci-gradle/; \
	elif [ -n "$$USERPROFILE" ] && [ -f "$$USERPROFILE\\.gradle\\gradle.properties" ]; then \
		cp "$$USERPROFILE\\.gradle\\gradle.properties" .ci-gradle/; \
	else \
		echo "[ERROR] gradle.properties не найден!"; exit 1; \
	fi
	docker build --build-arg GRADLE_USER_HOME=/tmp/.gradle \
		-t stop-list-gate:local \
		-f stop-list-gate/Dockerfile .
	rm -rf .ci-gradle

docker-build-processing:
	@echo "[INFO] Сборка docker-образа stop-list-processing с пробросом gradle.properties"
	@mkdir -p .ci-gradle
	@if [ -f "$$HOME/.gradle/gradle.properties" ]; then \
		cp "$$HOME/.gradle/gradle.properties" .ci-gradle/; \
	elif [ -n "$$USERPROFILE" ] && [ -f "$$USERPROFILE\\.gradle\\gradle.properties" ]; then \
		cp "$$USERPROFILE\\.gradle\\gradle.properties" .ci-gradle/; \
	else \
		echo "[ERROR] gradle.properties не найден!"; exit 1; \
	fi
	docker build --build-arg GRADLE_USER_HOME=/tmp/.gradle \
		-t stop-list-processing:local \
		-f stop-list-processing/Dockerfile .
	rm -rf .ci-gradle 