version: '3.7'

services:
  stop-list-gate:
    image: stop-list-gate:local
    container_name: stop-list-gate
    ports:
      - 5010:5000
      - 8010:8080
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    depends_on:
      - stop-list-db
      - kafka
      - s3
    environment:
      DB_URL: postgresql://stop_list_db:5432/stoplist
      R2DB_URL: ************************************************/stoplist
      DB_USER: postgres
      DB_PASSWORD: postgres
      KAFKA_SERVERS: kafka:29092
      S3_URL: http://s3:9000
      S3_ACCESS_KEY_ID: s3__user
      S3_SECRET_ACCESS_KEY: s3__pass
      S3_BUCKET: tkp3-sl
      CLICKHOUSE_URL: http://clickhouse-server:8123/dev
      BASE_URL: http://localhost:8010

  stop-list-processing:
    image: stop-list-processing:local
    container_name: stop-list-processing
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    depends_on:
      - stop-list-db
      - kafka
      - s3
    environment:
      DB_URL: postgresql://stop-list-db:5432/stoplist
      R2DB_URL: ************************************************/stoplist
      DB_USER: postgres
      DB_PASSWORD: postgres
      KAFKA_SERVERS: kafka:29092
      S3_URL: http://s3:9000
      S3_ACCESS_KEY_ID: s3__user
      S3_SECRET_ACCESS_KEY: s3__pass
      S3_BUCKET: tkp3-sl
      DIFF_ENABLE: "false"
      DEVELOP_EMISSION_STUB: "true"
      DEVELOP_S3_DISABLE_CHECKSUM: "true"
    networks:
      - tkp3

  stop-list-db:
    image: postgres:14
    container_name: stop-list-db
    restart: always
    ports:
      - 5432:5432
    volumes:
      - ./data/postgres:/var/lib/postgresql/data
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: stoplist
      PGDATA: /var/lib/postgresql/data
      POSTGRES_CONFIG: |
        log_statement = 'all'
        log_connections = on
        log_disconnections = on
    networks:
      tkp3:
#        ipv4_address: ***********  # r2dbc нужен ip, т.к. не резолвит dns внутри docker сети

  zookeeper:
    image: confluentinc/cp-zookeeper:7.9.1
    container_name: zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - tkp3

  kafka:
    image: confluentinc/cp-kafka:7.5.9
    container_name: kafka
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
    networks:
      - tkp3

  kafka-ui:
    image: provectuslabs/kafka-ui:v0.7.2
    container_name: kafka-ui
    depends_on:
      - kafka
    ports:
      - 8080:8080
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper:2181
    networks:
      - tkp3

  s3:
    image: minio/minio:RELEASE.2025-05-24T17-08-30Z
    container_name: s3
    environment:
      MINIO_ROOT_USER: s3__user
      MINIO_ROOT_PASSWORD: s3__pass
      MINIO_DOMAIN: s3 # переключение в режим virtual-hosts-style
    ports:
      - 9001:9001 # WebUI
    volumes:
      - ./data/minio:/data
    command: server --console-address ":9001" /data
    networks:
      tkp3:
        aliases:
          - tkp3-sl.s3

  clickhouse_server:
    image: yandex/clickhouse-server:latest # Or a specific version, e.g., 24.3.6
    container_name: clickhouse-server
    ports:
      - '8123:8123' # HTTP interface
      - '9000:9000' # Native ClickHouse client protocol
    volumes:
      - ./data/clickhouse:/var/lib/clickhouse # Persistent data storage
    ulimits:
      nofile: 262144 # Increase open file limit for ClickHouse
    environment:
      # Optional: Set environment variables for ClickHouse configuration
      # For example, to allow empty password for default user (for development)
      ALLOW_EMPTY_PASSWORD: 'yes'

networks:
  tkp3:
#    ipam:
#      config:
#        - subnet: **********/24  # Диапазон IP-адресов