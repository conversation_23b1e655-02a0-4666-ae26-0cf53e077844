syntax = "proto3";

package ru.sbertroika.stop.list.gate.v1;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "common-stop-list.proto";
import "common.proto";

option java_multiple_files = true;
option java_package = "ru.sbertroika.stop.list.gate.v1";

service StopListGatePrivateService {
  // Журнал записей стоп-листов
  rpc journalList(StopListJournalRequest) returns (StopListJournalResponse);
  // Добавить карту в стоп-лист
  rpc createEmvStopListRecord(CreateEmvStopListRecordRequest) returns (common.v1.EmptyResponse);
  // Получить все записи стоп-листа по проекту (последнюю версию)
  rpc emvStopListRecordList(EmvStopListRecordList) returns (EmvStopListRecordResponse);
  // Удалить карту из стоп-листа
  rpc deleteEmvStopListRecord(DeleteEmvStopListRecordRequest) returns (common.v1.EmptyResponse);
}

message StopListJournalRequest {
  optional common.v1.PaginationRequest pagination = 1;
  optional StopListJournalFilter filter = 2;
}

message StopListJournalFilter {
  optional string cardNum = 1;                                 // Номер носителя
  repeated common.stop.list.StopListType type = 2;             // Тип стоп-листа (emv, troika, prostor)
  optional google.protobuf.Timestamp createdAtFrom = 3;        // Дата формирования записи стоп-листа (в UTC+0) - с
  optional google.protobuf.Timestamp createdAtTo = 4;          // Дата формирования записи стоп-листа (в UTC+0) - по
  optional string hash = 5;                                    // Хэш значение от номера носителя
  optional common.stop.list.StopListSubType hashType = 6;      // Тип хеша (только для EMV)
  optional string uid = 7;                                     // Уникальный 8 байтный идентификатор в стоп-листе
  optional uint64 createVer = 8;                               // Номер версии в которой носитель был добавлен в стоп-лист
}

message StopListJournalResponse {
  oneof response {
    common.v1.OperationError error = 1;
    StopListJournalResult result = 2;
  }
}

message StopListJournalResult {
  optional common.v1.PaginationResponse pagination = 1;
  optional StopListJournalFilter filter = 2;
  repeated StopListRecord record = 3;
}

message StopListRecord {
  string projectId = 1;                           // Идентификатор проекта
  OperationType operation = 2;                    // Тип операции стоп-листа
  google.protobuf.Timestamp recordAt = 3;         // Дата формирования транзакции на сервере (в UTC+0)
  google.protobuf.Timestamp createdAt = 4;        // Дата формирования записи стоп-листа (в UTC+0)
  google.protobuf.Timestamp deletedAt = 5;        // Время удаления записи из стоп-листа (в UTC+0)
  string uid = 6;                                 // Уникальный 8 байтный идентификатор в стоп-листе
  string hash = 7;                                // Хеш значение от номера носителя
  common.stop.list.StopListSubType hashType = 8;  // Тип хеша
  uint64 createVer = 9;                           // Номер версии в которой носитель был добавлен в стоп-лист
  uint64 deleteVer = 10;                          // Номер версии в которой носитель был удален из стоп-листа
  common.stop.list.StopListType type = 11;        // Тип стоп-листа
}

enum OperationType {
    APPEND = 0;
    CHANGE = 1;
    DELETE = 2;
}

message CreateEmvStopListRecordRequest {
  string projectId = 1;                                    // Идентификатор проекта
  google.protobuf.Timestamp recordAt = 2;                  // Дата формирования транзакции на сервере (в UTC+0)
  optional string cardNum = 3;                             // Номер носителя
  optional string hash = 4;                                // Хеш значение от номера носителя
  optional common.stop.list.StopListSubType hashType = 5;  // Тип хеша
  uint64 createVer = 6;                                    // Номер версии в которой носитель был добавлен в стоп-лист
  common.stop.list.StopListType type = 7;                  // Тип стоп-листа
}

message EmvStopListRecordList {
  string projectId = 1;                                    // Идентификатор проекта
  optional common.v1.PaginationResponse pagination = 2;
}

message EmvStopListRecordResponse {
  oneof response {
    common.v1.OperationError error = 1;
    EmvStopListRecordResult result = 2;
  }
}

message EmvStopListRecordResult {
  repeated EmvStopListRecord records = 1;
}

message EmvStopListRecord {
  string recordId = 1;                                    // Идентификатор записи стоп-листа
  google.protobuf.Timestamp createdAt = 2;                // Дата формирования записи стоп-листа (в UTC+0)
  string hash = 3;                                        // Хэш значение от номера носителя
  common.stop.list.StopListSubType hashType = 4;          // Тип хеша (только для EMV)
  string uid = 5;                                         // Уникальный 8 байтный идентификатор в стоп-листе
  uint64 createVer = 6;                                   // Номер версии в которой носитель был добавлен в стоп-лист
}

message DeleteEmvStopListRecordRequest {
  string recordId = 1;                                    // Идентификатор записи стоп-листа
}