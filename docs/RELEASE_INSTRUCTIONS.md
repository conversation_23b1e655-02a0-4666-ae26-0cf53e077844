# Инструкция по выпуску релизов и публикации в Nexus

## Подготовка

1. Убедитесь, что все изменения закоммичены в ветку `master`.
2. Проверьте, что версия в корневом `gradle.properties` заканчивается на `-SNAPSHOT`.
3. Убедитесь, что в `gradle.properties` прописаны ваши учетные данные Nexus:

```properties
mavenUser=your-username
mavenPassword=your-password
```

## Выпуск релиза

Выполните команду:

```bash
./gradlew release
```

Эта команда:
- Уберет `-SNAPSHOT` из версии
- Создаст коммит и тег
- Соберет проект
- Опубликует артефакты в Nexus
- Увеличит версию до следующего SNAPSHOT

## Проверка

- Проверьте, что создан тег: `git tag -l`
- Проверьте публикацию артефактов в Nexus
- Убедитесь, что версия снова стала SNAPSHOT

## Публикация вручную

- SNAPSHOT:
  ```bash
  ./gradlew publishToNexusSnapshot
  ```
- Релиз:
  ```bash
  ./gradlew publishToNexus
  ```

## Проверка версий

```bash
./gradlew properties --property=version
```

## Troubleshooting

- Ошибка аутентификации: проверьте `mavenUser` и `mavenPassword` в gradle.properties
- Ошибка requireBranch: убедитесь, что вы находитесь в ветке master
- Ошибка failOnCommitNeeded: все изменения должны быть закоммичены
- Разные версии: версия только в корневом gradle.properties, в модулях не должно быть своих версий

## Артефакты в Nexus

- `ru.sbertroika.stop-list:common-stop-list:${version}`
- `ru.sbertroika.stop-list:stop-list-api:${version}`
- `ru.sbertroika.stop-list:stop-list-private-api:${version}`
- `ru.sbertroika.stop-list:stop-list-model:${version}`
- `ru.sbertroika.stop-list:lib-stop-list:${version}` 