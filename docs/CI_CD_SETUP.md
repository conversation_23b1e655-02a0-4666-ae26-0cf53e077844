# CI/CD Pipeline Setup

## Обзор

Этот документ описывает настройку и работу CI/CD pipeline для проекта `stop-list-domain` на базе GitLab CI/CD с Docker executor.

## Переменные окружения

В настройках проекта GitLab → Settings → CI/CD → Variables добавьте:

| Переменная               | Описание               | Protected | Masked |
|--------------------------|------------------------|-----------|--------|
| `MAVEN_USER`             | Пользователь Nexus     | ✅        |        |
| `MAVEN_PASSWORD`         | Пароль Nexus           | ✅        | ✅      |
| `DOCKER_REPOSITORY_ADDR` | Docker registry        | ✅        |        |
| `KUBECONFIG_DEVELOP`     | kubeconfig для dev     | ✅        |        |

## Структура Pipeline

- **build** — сборка проекта
- **test** — запуск тестов
- **publish-snapshot** — публикация SNAPSHOT в Nexus
- **release** — публикация релиза и тегирование
- **deploy** — деплой микросервисов через Helm

### Сценарии

1. **Любая ветка (кроме develop/master)**
   - Сборка и тестирование
2. **develop**
   - Сборка, тесты, публикация SNAPSHOT, сборка и деплой микросервисов в dev
3. **master / теги**
   - Выпуск релиза, публикация в Nexus, деплой в production

## Локальное тестирование pipeline

Для имитации CI/CD используйте скрипт:

```bash
./test-docker-ci.sh all
```

Или по шагам:
```bash
./test-docker-ci.sh build
./test-docker-ci.sh test
./test-docker-ci.sh publish-snapshot
```

## Безопасность

- Все секреты передаются только через защищённые переменные GitLab
- Используются официальные Docker-образы
- Кэш Gradle изолирован

## Troubleshooting

- Ошибка аутентификации: проверьте переменные `MAVEN_USER` и `MAVEN_PASSWORD`
- Ошибка прав: runner должен запускаться с docker executor
- Ошибка публикации: проверьте доступность Nexus и Docker registry

## Дополнительно

- Для настройки runner используйте официальную документацию GitLab
- Для деплоя в Kubernetes используются Helm чарты из директории `charts/` 