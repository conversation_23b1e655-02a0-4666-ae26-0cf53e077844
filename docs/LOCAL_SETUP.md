# Локальная сборка и запуск stop-list-domain

## Предварительные требования
- Java 17
- Docker и Docker Compose
- Gradle 8.x (или используйте gradlew)
- Git

## Клонирование репозитория
```bash
git clone <repository-url>
cd stop-list-domain
```

## Сборка библиотек и микросервисов
```bash
./gradlew build
```

## Запуск инфраструктуры (Postgres, Kafka, S3, Clickhouse)
```bash
docker-compose up -d stop-list-db kafka s3 clickhouse-server kafka-ui s3-ui
```

- S3 UI: http://localhost:9001 (логин: s3__user, пароль: s3__pass)
- Kafka UI: http://localhost:8080
- Postgres: localhost:5432

## Сборка и запуск микросервисов
```bash
# Сборка Docker образов
docker build -f stop-list-gate/Dockerfile -t stop-list-gate:local .
docker build -f stop-list-processing/Dockerfile -t stop-list-processing:local .

# Запуск микросервисов
docker-compose up stop-list-gate stop-list-processing
```

## Доступные сервисы
- API Gateway: http://localhost:8010
- Stop List Processing: (порт см. docker-compose)

## Остановка сервисов
```bash
docker-compose down
```

## Troubleshooting
- Проверьте, что все порты свободны
- Проверяйте логи контейнеров: `docker-compose logs <service>`
- Для работы с S3 создайте bucket через UI 