#!/bin/bash

# Скрипт для локального тестирования в Docker
# Использование: ./scripts/docker-test.sh [build|test|publish-snapshot|publish-release]

set -e

# Проверяем, что мы в корне проекта
if [ ! -f "gradlew" ]; then
    echo "Ошибка: Запустите скрипт из корня проекта"
    exit 1
fi

# Проверяем, что Docker доступен
if ! command -v docker &> /dev/null; then
    echo "Ошибка: Docker не установлен или не доступен"
    exit 1
fi

# Проверяем, что локальный gradle.properties существует
if [ ! -f "$HOME/.gradle/gradle.properties" ]; then
    echo "Предупреждение: $HOME/.gradle/gradle.properties не найден"
    echo "Создайте файл с учетными данными Nexus:"
    echo "mavenUser=your-username"
    echo "mavenPassword=your-password"
fi

# Функция для выполнения команды в Docker
run_in_docker() {
    local command="$1"
    echo "Выполняем в Docker: $command"
    
    docker run --rm \
        -v "$(pwd):/app" \
        -v "$HOME/.gradle:/root/.gradle" \
        -w /app \
        openjdk:17-jdk-slim \
        bash -c "
            export JAVA_HOME=\$(dirname \$(dirname \$(readlink -f \$(which java))))
            export PATH=\$JAVA_HOME/bin:\$PATH
            chmod +x ./gradlew
            $command
        "
}

# Обработка аргументов
case "${1:-build}" in
    "build")
        echo "🔨 Сборка проекта в Docker..."
        run_in_docker "./gradlew build --no-daemon"
        ;;
    "test")
        echo "🧪 Запуск тестов в Docker..."
        run_in_docker "./gradlew test --no-daemon"
        ;;
    "publish-snapshot")
        echo "📦 Публикация SNAPSHOT в Docker..."
        run_in_docker "./gradlew publishToNexusSnapshot --no-daemon"
        ;;
    "publish-release")
        echo "🚀 Публикация релиза в Docker..."
        run_in_docker "./gradlew publishToNexus --no-daemon"
        ;;
    "all")
        echo "🔄 Полный цикл: сборка → тесты → публикация SNAPSHOT..."
        run_in_docker "./gradlew build test publishToNexusSnapshot --no-daemon"
        ;;
    *)
        echo "Использование: $0 [build|test|publish-snapshot|publish-release|all]"
        echo ""
        echo "Команды:"
        echo "  build              - Сборка проекта"
        echo "  test               - Запуск тестов"
        echo "  publish-snapshot   - Публикация SNAPSHOT версии"
        echo "  publish-release    - Публикация релизной версии"
        echo "  all                - Полный цикл (сборка + тесты + SNAPSHOT)"
        exit 1
        ;;
esac

echo "✅ Команда выполнена успешно!" 